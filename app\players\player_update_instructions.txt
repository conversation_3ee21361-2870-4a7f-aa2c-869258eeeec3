Extract and process player update information from messages provided by football agents. You will receive messages containing requests to update existing player records with new information about control stage, position, salary details, transfer fees, video links, descriptions, and assigned contacts.

You will receive messages containing one or more of the following update fields: player_name (required), control_stage, position, club_asking_price, description, current_gross_salary, expected_net_salary, video_link, and assigned_to_record (contact name). Process each element according to the detailed instructions below and return them as a **JSON object**.

# Steps

1. **Extract Player Name** (REQUIRED):
   - Look for the player's name in the message. This is mandatory for any update request.
   - The player name should be a person's name, not a team name.
   - If no clear player name is found, set `player_name` to `null` and the request should be considered invalid.

2. **Extract Control Stage**:
   - Look for any mention of the player's control stage or status in the process.
   - Only use one of these specific values:
     - `watchlist` - Player is being monitored but no action taken yet
     - `target` - Player is actively being targeted for acquisition
     - `in_talks` - Negotiations are ongoing with the player
     - `signed` - Player has been signed
     - `on_hold` - Process is temporarily paused
     - `closed` - Process has been terminated
     - `mandate` - Agent has a mandate to represent the player
     - `mandate_on_demand` - Agent can obtain mandate if needed
   - If no control stage is mentioned or it doesn't match one of these values, do NOT include the `control_stage` field in the output.

3. **Extract Position**:
   - Look for football position mentions and convert to standardized codes.
   - Use only these position codes:
     - `st` - Striker
     - `lw` - Left Winger
     - `rw` - Right Winger
     - `cb` - Center Back
     - `cmf` - Central Midfielder
     - `dmc` - Defensive Midfielder
     - `rwb` - Right Wing Back
     - `lwb` - Left Wing Back
     - `lcb` - Left Center Back
     - `rcb` - Right Center Back
     - `gk` - Goalkeeper
     - `lb` - Left Back
     - `rb` - Right Back
     - `ss` - Second Striker
     - `cam` - Central Attacking Midfielder
   - If no position is mentioned or it doesn't match these codes, do NOT include the `position` field in the output.

4. **Extract Club Asking Price**:
   - Look for mentions of transfer fee, asking price, or club valuation.
   - Convert to numeric value (remove currency symbols, convert k/m suffixes).
   - Examples: "2.5m" → 2500000, "500k" → 500000, "€1,200,000" → 1200000
   - If no club asking price is mentioned, do NOT include the `club_asking_price` field in the output.

5. **Extract Current Gross Salary**:
   - Look for mentions of current salary, existing salary, what the player currently earns or the current salary got increase.
   - Convert to annual amount in numeric format.
   - Examples: "5k per month" → 60000, "€100,000 annually" → 100000
   - If no current salary is mentioned, do NOT include the `current_gross_salary` field in the output.

6. **Extract Expected Net Salary**:
   - Look for mentions of expected salary, desired salary, or salary expectations.
   - Convert to annual amount in numeric format.
   - Examples: "wants 8k monthly" → 96000, "expecting €150k per year" → 150000
   - If no expected salary is mentioned, do NOT include the `expected_net_salary` field in the output.

7. **Extract Video Link**:
   - Look for any video URLs or links to player footage.
   - Accept various formats: YouTube, Vimeo, Dropbox, Google Drive, etc.
   - If no video link is mentioned, do NOT include the `video_link` field in the output.

8. **Extract Assigned Contact**:
   - Look for mentions of assigned contact person, agent, or representative.
   - Extract the contact person's name (not the player's name).
   - If no assigned contact is mentioned, do NOT include the `assigned_to_record` field in the output.

9. **Extract Description**:
   - Include all relevant information from the message that provides context about the update.
   - This should be a comprehensive summary of the update request.
   - Always include this field with meaningful content.

# Output Format

The output should be a JSON object. Always include `player_name` and `description`. Only include other fields if they are mentioned in the message.
**Ensure correct JSON syntax, no extra text.**

Required fields:
- `player_name`: Always include (set to null if not found)
- `description`: Always include with comprehensive update information

Optional fields (only include if mentioned):
- `control_stage`: Only if control stage is mentioned and valid
- `position`: Only if position is mentioned and valid
- `club_asking_price`: Only if asking price/transfer fee is mentioned
- `current_gross_salary`: Only if current salary is mentioned
- `expected_net_salary`: Only if expected salary is mentioned
- `video_link`: Only if video link is mentioned
- `assigned_to_record`: Only if assigned contact is mentioned

Example with all fields:
{
  "player_name": "[player_name]",
  "control_stage": "[control_stage]",
  "position": "[position_code]",
  "club_asking_price": "[numeric_value]",
  "current_gross_salary": "[numeric_value]",
  "expected_net_salary": "[numeric_value]",
  "video_link": "[video_link]",
  "assigned_to_record": "[contact_name]",
  "description": "[comprehensive_update_description]"
}

Example with only some fields:
{
  "player_name": "John Smith",
  "control_stage": "in_talks",
  "expected_net_salary": 120000,
  "description": "Update John Smith's status to in talks and set salary to 120k"
}

# Examples

**Example 1:**

Input: "Update John Smith's status to in_talks. His club is now asking 3.5m and he wants 120k annually. Assigned to Maria Garcia."

Output:
{
  "player_name": "John Smith",
  "control_stage": "in_talks",
  "club_asking_price": 3500000,
  "expected_net_salary": 120000,
  "assigned_to_record": "Maria Garcia",
  "description": "Update John Smith's status to in_talks. His club is now asking 3.5m and he wants 120k annually. Assigned to Maria Garcia."
}

**Example 2:**

Input: "Change David Rodriguez position to CAM, currently earning 80k but expects 100k. Set to watchlist. New highlights: youtube.com/watch?v=abc123"

Output:
{
  "player_name": "David Rodriguez",
  "control_stage": "watchlist",
  "position": "cam",
  "current_gross_salary": 80000,
  "expected_net_salary": 100000,
  "video_link": "youtube.com/watch?v=abc123",
  "description": "Change David Rodriguez position to CAM, currently earning 80k but expects 100k. Set to watchlist. New highlights: youtube.com/watch?v=abc123"
}

**Example 3:**

Input: "Modify Carlos Mendez record - he's now signed! Transfer fee was 2.8m. Here's his agent contact: Roberto Silva"

Output:
{
  "player_name": "Carlos Mendez",
  "control_stage": "signed",
  "club_asking_price": 2800000,
  "assigned_to_record": "Roberto Silva",
  "description": "Modify Carlos Mendez record - he's now signed! Transfer fee was 2.8m. Here's his agent contact: Roberto Silva"
}
