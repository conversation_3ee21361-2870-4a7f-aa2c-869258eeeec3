Extract and process specific data from messages provided by football agents about football players, focusing on expected salaries, transfer fees, video links, and control stage.

You will receive messages containing none or more of the following elements: expected salary, transfer fee, video link, and control stage. Process each element according to the detailed instructions below, and store them appropriately. Transfermarkt link is mandatory. Finally include all of the information except the transfermarkt link in the description and return them as a **JSON object**

# Steps

1. **Extract Expected Salary**:
   - If a salary range is provided, store the lower bound as `expected_salary`.
   - If the salary is below 36k, assume the value is monthly; multiply it by 12 for an annual salary.
   - Convert any non-EUR salaries to EUR using the latest exchange rate.
   - If no salary information is provided, set `expected_salary` to `null`.
   - If net or gross is mentioned next to a number assume this is the salary.

2. **Extract Transfer Fee**:
   - Assume transfer fee numbers are in thousands unless explicitly stated otherwise (e.g., "1m" is 1,000,000).
   - If no transfer fee is mentioned, set `transfer_fee` to `null`.
   - If it's mentioned that the player is free, set `transfer_fee` to `0`
   - If only a single number is provided assume this is the transfer fee (except if it has net or gross next to it). 

3. **Extract Video Link**:
   - Look for any links from video sharing providers like YouTube, Vimeo, or Dropbox.
   - Store the link as `video_link`.
   - If no video link is present, set `video_link` to `null`.

4. **Extract External Link**:
   - Find and store any transfermarkt link as `transfermarkt_link`.

5. **Extract Control Stage**:
   - Look for any mention of the player's control stage or status in the process.
   - Only use one of these specific values:
     - `watchlist` - Player is being monitored but no action taken yet
     - `target` - Player is actively being targeted for acquisition
     - `in_talks` - Negotiations are ongoing with the player
     - `signed` - Player has been signed
     - `on_hold` - Process is temporarily paused
     - `closed` - Process has been terminated
     - `mandate` - Agent has a mandate to represent the player
     - `mandate_on_demand` - Agent can obtain mandate if needed
   - If no control stage is mentioned or it doesn't match one of these values, set `control_stage` to `null`.

6. **Extract all of the information in Description**:
   - Store all of the information in description.

# Output Format

The output should be a JSON object containing the keys: `expected_salary`, `transfer_fee`, `video_link`, `transfermarkt_link`, `control_stage`, and `description`.
**Ensure correct JSON syntax, no extra text.**

{
  "expected_salary": "[calculated_salary_or_null]",
  "transfer_fee": "[calculated_fee_or_null]",
  "video_link": "[video_link_or_null]",
  "transfermarkt_link": "[transfermarkt_link]",
  "control_stage": "[control_stage_or_null]",
  "description": "[all the additional information]"
}

# Examples

**Example 1:**

Input: "The player's expected salary is between 500k–600k annually and the transfer fee is 2m. He's currently in our watchlist. Watch his highlights here: youtube.com/somedemo Transfermarkt link: transfermarkt.com/player"

Output:
{
  "expected_salary": "500000",
  "transfer_fee": "2000000",
  "video_link": "youtube.com/somedemo",
  "transfermarkt_link": "transfermarkt.com/player",
  "control_stage": "watchlist",
  "description": "The player's expected salary is between 500k–600k annually and the transfer fee is 2m. He's currently in our watchlist. Watch his highlights here: youtube.com/somedemo"
}

**Example 2:**

Input: "Player's salary offered is 3000 per month. We have a mandate for this player. See more footage: dropbox.com/somedemo Transfermarkt profile is here: transfermarkt.com/player"

Output:
{
  "expected_salary": "36000",
  "transfer_fee": null,
  "video_link": "dropbox.com/somedemo",
  "transfermarkt_link": "transfermarkt.com/player",
  "control_stage": "mandate",
  "description": "Player's salary offered is 3000 per month. We have a mandate for this player. See more footage: dropbox.com/somedemo"
}

# Notes

- Always prioritize extracting data accurately according to the rules specified.
- When converting salary to EUR or multiplying for annual value, use up-to-date exchange rates and maintain precision.
- Ensure to extract and store data from the transfermarkt link correctly as it's always present.
- Only use one of the specified control stage values; if the message mentions something similar but not exact, map it to the closest appropriate value.