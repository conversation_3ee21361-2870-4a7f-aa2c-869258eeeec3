"""
Contact lookup utility functions for consistent contact handling across handlers.
Used by both player update handler and activity handler.
"""

import logging
from typing import Dict, Any, Optional, Tuple
from app.utils.secure_http_client import get_secure_http_client

logger = logging.getLogger(__name__)


async def lookup_contact(
    contact_name: str, phone_number: str, sender: str
) -> Tuple[Optional[str], Optional[Dict[str, Any]]]:
    """
    Look up a contact using the WhatsApp check_contact endpoint.

    Args:
        contact_name: Name of the contact to look up
        phone_number: Phone number of the WhatsApp sender (for authorization)
        sender: WhatsApp sender ID (for error messaging)

    Returns:
        Tuple of (contact_id, contact_data)
        - contact_id: UUID of the contact if found, None otherwise
        - contact_data: Full contact data dictionary if found, None otherwise
    """
    if not contact_name or contact_name.strip() == "":
        logger.info("⏭️ Skipping contact lookup - no contact name provided")
        return None, None

    # Skip lookup for default values
    if contact_name.lower() in ["sender_default", "you", "unknown"]:
        logger.info(f"⏭️ Skipping contact lookup for default value: '{contact_name}'")
        return None, None

    logger.info(f"🔍 Looking up contact: '{contact_name}'")

    try:
        client = get_secure_http_client()
        contact_params = {
            "phone_number": phone_number,
            "contact_name": contact_name,
        }

        contact_response = client.get(
            "/contacts/whatsapp/check_contact",
            params=contact_params,
            sender=sender,
            timeout=10,
        )

        if contact_response.success:
            contact_data = contact_response.data
            contact_id = contact_data.get("contact_id")

            if contact_id:
                contact_full_name = contact_data.get(
                    "fullName", contact_data.get("name", contact_name)
                )
                logger.info(
                    f"✅ Contact lookup successful: '{contact_name}' -> '{contact_full_name}' (ID: {contact_id})"
                )
                return contact_id, contact_data
            else:
                logger.warning(
                    f"⚠️ Contact '{contact_name}' found but no contact_id returned"
                )
                return None, contact_data
        else:
            # Only log if not auth/rate limit error (those are handled by client)
            if contact_response.status_code not in [401, 403, 429]:
                logger.warning(
                    f"❌ Contact '{contact_name}' not found (status: {contact_response.status_code})"
                )
            # Continue without contact - this is not a blocking error
            return None, None

    except Exception as e:
        logger.warning(f"💥 Exception during contact lookup for '{contact_name}': {e}")
        return None, None


def format_contact_for_activity(
    contact_data: Optional[Dict[str, Any]],
) -> Dict[str, Any]:
    """
    Format contact data for activity creation.

    Args:
        contact_data: Contact data dictionary from lookup

    Returns:
        Dictionary with formatted contact information for activities
    """
    if not contact_data:
        return {
            "assigned_to_record": [],
            "assigned_to": "you",
        }

    contact_id = contact_data.get("contact_id")
    contact_name = contact_data.get("fullName", contact_data.get("name", "Unknown"))

    return {
        "assigned_to_record": (
            [contact_id] if contact_id else []
        ),  # API expects list of UUIDs
        "assigned_to": contact_name,  # Display name
    }


def format_contact_for_player_update(
    contact_data: Optional[Dict[str, Any]],
) -> Optional[Dict[str, Any]]:
    """
    Format contact data for player update.

    Args:
        contact_data: Contact data dictionary from lookup

    Returns:
        Formatted contact dictionary for player updates or None
    """
    if not contact_data:
        return None

    contact_id = contact_data.get("contact_id")
    if not contact_id:
        return None

    return {
        "contact_id": contact_id,
        "contactId": contact_id,  # Use contact_id for both fields
        "fullName": contact_data.get("fullName", "Unknown"),
        "similarity_score": contact_data.get("similarity_score", 1.0),
        "top_matches": contact_data.get("top_matches", []),
    }
