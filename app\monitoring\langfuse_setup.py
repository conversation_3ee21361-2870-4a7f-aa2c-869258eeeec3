import os
import logging
from typing import Optional, Dict, Any
from langfuse import <PERSON><PERSON>, get_client
from app.config import settings

logger = logging.getLogger(__name__)

# Global LangFuse client instance
_langfuse_client: Optional[Langfuse] = None


def setup_langfuse() -> Optional[Langfuse]:
    """
    Setup LangFuse integration for monitoring router agent performance.

    Returns:
        LangFuse client instance if successful, None otherwise
    """
    global _langfuse_client

    try:
        # Validate required environment variables
        # Try both LANGFUSE_SECRET_KEY (v3) and LANGFUSE_API_KEY (your current setup)
        api_key = os.getenv("LANGFUSE_SECRET_KEY") or os.getenv("LANGFUSE_API_KEY")
        if not api_key:
            logger.warning(
                "LangFuse secret key not configured (LANGFUSE_SECRET_KEY or LANGFUSE_API_KEY)"
            )
            return None

        public_key = os.getenv("LANGFUSE_PUBLIC_KEY")
        if not public_key:
            logger.warning("LangFuse public key not configured (LANGFUSE_PUBLIC_KEY)")
            return None

        # Try both LANGFUSE_HOST (v3) and LANGFUSE_API_HOST (your current setup)
        host = os.getenv("LANGFUSE_HOST") or os.getenv(
            "LANGFUSE_API_HOST", "https://cloud.langfuse.com"
        )

        # Initialize LangFuse client
        _langfuse_client = Langfuse(
            secret_key=api_key, public_key=public_key, host=host
        )

        # Test the connection
        try:
            if _langfuse_client.auth_check():
                logger.info("LangFuse connected successfully")
            else:
                logger.warning("LangFuse authentication failed")
                _langfuse_client = None
                return None

        except Exception as e:
            logger.warning(f"LangFuse connection test failed: {e}")
            _langfuse_client = None
            return None

        logger.info(f"LangFuse monitoring initialized for host: {host}")
        return _langfuse_client

    except Exception as e:
        logger.error(f"Failed to setup LangFuse: {e}")
        _langfuse_client = None
        return None


def get_langfuse_client() -> Optional[Langfuse]:
    """
    Get the global LangFuse client instance.

    Returns:
        LangFuse client instance if available, None otherwise
    """
    global _langfuse_client

    if _langfuse_client is None:
        _langfuse_client = setup_langfuse()

    return _langfuse_client


def log_router_decision(
    message: str,
    sender: str,
    function_name: str,
    confidence: float,
    reasoning: str,
    execution_time: float,
):
    """
    Log router decision to LangFuse for analysis.

    Args:
        message: The original user message
        sender: Sender identifier
        function_name: Selected function name
        confidence: Router confidence score
        reasoning: Router reasoning
        execution_time: Time taken for routing decision
    """
    # NOTE: This function is deprecated. Router decisions are now traced
    # automatically via the @observe decorator on router functions.
    logger.info(
        f"Router decision: {function_name} (confidence: {confidence:.2f}, time: {execution_time:.3f}s)"
    )


def log_handler_execution(
    handler_name: str,
    sender: str,
    execution_time: float,
    success: bool,
    error_message: Optional[str] = None,
):
    """
    Log handler execution to LangFuse for performance monitoring.

    Args:
        handler_name: Name of the executed handler
        sender: Sender identifier
        execution_time: Handler execution time
        success: Whether execution was successful
        error_message: Error message if execution failed
    """
    # NOTE: This function is deprecated. Handler executions are now traced
    # automatically via the @observe decorator on handler functions.
    logger.info(
        f"Handler execution: {handler_name} ({'success' if success else 'failed'}, time: {execution_time:.3f}s)"
    )


def get_monitoring_dashboard_url() -> Optional[str]:
    """
    Get the URL for the LangFuse monitoring dashboard.

    Returns:
        Dashboard URL if LangFuse is configured, None otherwise
    """
    if not get_langfuse_client():
        return None

    host = os.getenv("LANGFUSE_API_HOST", "https://cloud.langfuse.com")

    # Convert API host to web dashboard URL
    if host.endswith("/api"):
        dashboard_url = host[:-4]  # Remove /api suffix
    else:
        dashboard_url = host

    return dashboard_url


def validate_langfuse_config() -> Dict[str, Any]:
    """
    Validate LangFuse configuration and return status.

    Returns:
        Dictionary with configuration status
    """
    status = {
        "enabled": False,
        "configured": False,
        "connected": False,
        "host": None,
        "dashboard_url": None,
        "issues": [],
    }

    # Check API key
    api_key = os.getenv("LANGFUSE_API_KEY")
    if not api_key:
        status["issues"].append("LANGFUSE_API_KEY not configured")
        return status

    # Check public key
    public_key = os.getenv("LANGFUSE_PUBLIC_KEY")
    if not public_key:
        status["issues"].append("LANGFUSE_PUBLIC_KEY not configured")
        return status

    status["configured"] = True
    status["enabled"] = True

    # Test connection
    client = get_langfuse_client()
    if client:
        status["connected"] = True
        status["host"] = os.getenv("LANGFUSE_API_HOST", "https://cloud.langfuse.com")
        status["dashboard_url"] = get_monitoring_dashboard_url()
    else:
        status["issues"].append("Failed to connect to LangFuse")

    return status


def flush_langfuse():
    """
    Flush any pending LangFuse traces.
    Should be called before application shutdown.
    """
    client = get_langfuse_client()
    if client:
        try:
            client.flush()
            logger.info("LangFuse traces flushed successfully")
        except Exception as e:
            logger.warning(f"Failed to flush LangFuse traces: {e}")
