import re
from app.utils.whatsapp_handler import (
    send_whatsapp_message,
)
import requests
import json
import time
import logging
from langfuse import observe
from app.config import settings
from app.db import insert_whatsapp_request
from app.monitoring.whatsapp_tracer import trace_assistant, trace_response, trace_error
from app.utils.ai_client import get_ai_client
from app.utils import get_absolute_path
from app.utils.secure_http_client import get_secure_http_client


def _load_player_instructions():
    """Load player assistant instructions from file."""
    try:
        instructions_path = get_absolute_path(
            "app/players/player_assistant_instructions.txt"
        )
        with open(instructions_path, "r", encoding="utf-8") as f:
            return f.read().strip()
    except FileNotFoundError:
        raise FileNotFoundError("player_assistant_instructions.txt not found")


@observe(name="Player Handler", as_type="generation")
async def process_player_request(
    sender: str, message: str, client, user_sessions: dict, trace_id: str = None
):
    """Handle player request processing using OpenAI assistant"""
    try:
        # Extract Transfermarkt URL
        transfermarkt_match = re.search(
            r"https?://(www\.)?transfermarkt\.(com|com.ar|co.uk|de|it|ar|co|fr|es|nl|ru|pt|pl|tr|gr|cz|ro|hu|ua|br|ar|mx|ch|at|dk|se|no|fi|be|hr|rs|bg|sk|si|ba|ie|lu|mt|cy|lt|lv|ee|us)/.*/spieler/\d+",
            message,
        )
        if not transfermarkt_match:
            send_whatsapp_message(
                sender, "⚠ Please include a valid Transfermarkt player profile link."
            )
            return

        try:
            insert_whatsapp_request(sender, message)
        except Exception as e:
            print("Error saving the player log", e)
        transfermarkt_url = transfermarkt_match.group()

        # ✅ Step 1: AI Processing (Gemini/OpenAI call)
        from langfuse import get_client

        langfuse = get_client()
        ai_client = get_ai_client()

        with langfuse.start_as_current_generation(
            name="AI Processing",
            model="gemini-2.5-flash-lite-preview-06-17",
            input={"message": message, "transfermarkt_url": transfermarkt_url},
            metadata={"component": "player_handler", "step": "ai_processing"},
        ) as ai_span:
            assistant_start_time = time.perf_counter()

            player_instructions = _load_player_instructions()

            try:
                ai_response = await ai_client.chat_completion(
                    messages=[
                        {"role": "system", "content": player_instructions},
                        {"role": "user", "content": message},
                    ],
                    model="gemini-2.5-flash-lite-preview-06-17",
                    temperature=0.1,
                    max_tokens=1500,
                    response_format={"type": "json_object"},
                )

                assistant_duration = time.perf_counter() - assistant_start_time

                # Parse response with better debugging
                assistant_response = None
                if ai_response.content and ai_response.content.strip():
                    try:
                        logging.info(
                            f"Raw player AI response content: '{ai_response.content[:200]}...'"
                        )
                        assistant_response = json.loads(ai_response.content.strip())
                    except json.JSONDecodeError as e:
                        logging.error(f"Failed to parse player JSON response: {e}")
                        logging.error(
                            f"Raw content that failed: '{ai_response.content}'"
                        )
                        assistant_response = None
                else:
                    logging.error(
                        f"Empty or None response content from {ai_response.provider.value}"
                    )
                    logging.error(f"Full response object: {ai_response}")
                    assistant_response = None

                # Update AI span with results
                ai_span.update(
                    output=assistant_response,
                    usage_details={
                        "input_tokens": (
                            ai_response.usage["prompt_tokens"]
                            if ai_response.usage
                            else 0
                        ),
                        "output_tokens": (
                            ai_response.usage["completion_tokens"]
                            if ai_response.usage
                            else 0
                        ),
                        "total_tokens": (
                            ai_response.usage["total_tokens"]
                            if ai_response.usage
                            else 0
                        ),
                    },
                )

            except Exception as e:
                logging.error(f"Player AI API call failed: {e}")
                ai_span.update(output={"error": str(e)})
                send_whatsapp_message(
                    sender, "⚠ Error processing player request. Please try again."
                )
                return

        # Trace assistant call
        if trace_id:
            await trace_assistant(
                trace_id,
                f"chat-completions-{ai_response.model}-player",
                message,
                {
                    "response_received": assistant_response is not None,
                    "model": ai_response.model,
                    "provider": ai_response.provider.value,
                    "tokens_used": (
                        ai_response.usage["total_tokens"] if ai_response.usage else None
                    ),
                },
                assistant_duration,
            )

        if not assistant_response or isinstance(assistant_response, str):
            error_msg = "⚠ Could not process player data. Please ensure the message contains a valid Transfermarkt link and relevant details."
            send_whatsapp_message(sender, error_msg)

            # Trace error response
            if trace_id:
                await trace_response(trace_id, sender, error_msg, False)
            return

        # ✅ Step 2: Player Data Lookup (session storage + response formatting)
        with langfuse.start_as_current_span(
            name="Player Data Lookup",
            input={
                "assistant_response": assistant_response,
                "transfermarkt_url": transfermarkt_url,
            },
            metadata={"component": "player_handler", "step": "data_lookup_and_format"},
        ) as lookup_span:
            # Store parsed data in session
            user_sessions[sender] = {
                "type": "player",
                "data": {
                    "transfermarkt_link": transfermarkt_url,
                    "transfer_fee": assistant_response.get("transfer_fee"),
                    "expected_salary": assistant_response.get("expected_salary"),
                    "video_link": assistant_response.get("video_link"),
                    "description": assistant_response.get("description"),
                    "control_stage": assistant_response.get(
                        "control_stage"
                    ),  # Add control stage
                },
            }

            # Build confirmation message
            confirmation_msg = "📝 *Player Request Details:*\n"
            if assistant_response.get("expected_salary"):
                confirmation_msg += (
                    f"💰 Asking Salary: {assistant_response['expected_salary']}\n"
                )
            if assistant_response.get("transfer_fee"):
                confirmation_msg += (
                    f"📤 Transfer Fee: {assistant_response['transfer_fee']}\n"
                )
            if assistant_response.get("video_link"):
                confirmation_msg += (
                    f"🎥 Video Link: {assistant_response['video_link']}\n"
                )
            if assistant_response.get("control_stage"):
                confirmation_msg += (
                    f"🎮 Control Stage: {assistant_response['control_stage']}\n"
                )

            await handle_player_creation(
                sender, user_sessions[sender]["data"], user_sessions
            )

            # Update lookup span with results
            lookup_span.update(
                output={
                    "session_stored": True,
                    "player_creation_called": True,
                }
            )

    except Exception as e:
        send_whatsapp_message(
            sender, "⚠ Error processing player request. Please try again."
        )
        print(f"Player processing error: {str(e)}")


async def handle_player_creation(sender: str, player_data: dict, user_sessions: dict):
    """Handle final player submission"""

    phone_number = sender.split(":")[1]

    if player_data["transfer_fee"] == "null":
        player_data["transfer_fee"] = None
    if player_data["expected_salary"] == "null":
        player_data["expected_salary"] = None
    if player_data["video_link"] == "null":
        player_data["video_link"] = None
    if player_data["description"] == "null":
        player_data["description"] = None
    if player_data.get("control_stage") == "null":
        player_data["control_stage"] = None
    try:
        # Prepare payload for API
        payload = {
            "club_asking_price": player_data.get("transfer_fee"),
            "expected_net_salary": player_data.get("expected_salary"),
            "video_link": player_data.get("video_link"),
            "description": player_data.get("description"),
            "control_stage": player_data.get(
                "control_stage"
            ),  # Add control stage to payload
        }
        splitted_tm_link = player_data["transfermarkt_link"].split("/")
        tm_player_id = (
            splitted_tm_link[-1] if splitted_tm_link[-1] else splitted_tm_link[-2]
        )
        payload["tm_player_id"] = tm_player_id

        # Make secure API call
        client = get_secure_http_client()
        api_response = client.post(
            "/player_records/whatsapp",
            json_data=payload,
            params={"phone_number": phone_number},
            sender=sender,
        )

        # Handle successful response
        if api_response.success:
            tokens_left = api_response.data.get("tokens_left", "N/A")
            send_whatsapp_message(
                sender,
                f"✅ Player record created successfully! Tokens left: {tokens_left}",
            )
        else:
            # Handle specific business logic errors (not auth/rate limit - those are handled by client)
            if api_response.status_code == 409:
                error_detail = api_response.data.get("detail", "")
                if error_detail == "tm_data_not_processed":
                    send_whatsapp_message(
                        sender, "⚠ Transfermarkt data hasn't been processed yet."
                    )
                elif error_detail == "player_already_exists":
                    send_whatsapp_message(
                        sender, "⚠ This player already exists in our system."
                    )
                else:
                    send_whatsapp_message(
                        sender, "⚠ Player record conflict. Please check your data."
                    )
            elif api_response.status_code == 402:
                tokens_left = api_response.data.get("tokens_left", 0)
                send_whatsapp_message(
                    sender,
                    f"⚠ Not enough tokens to create player record! Tokens left: {tokens_left}",
                )
            elif api_response.status_code not in [401, 403, 429]:
                # Don't send additional messages for auth/rate limit errors (handled by client)
                send_whatsapp_message(
                    sender, "⚠ Error submitting player record. Please try again."
                )

        # Clear session regardless of outcome
        del user_sessions[sender]

    except Exception as e:
        send_whatsapp_message(sender, "⚠ Unexpected error occurred.")
        logger.error(f"Player submission error: {str(e)}")
