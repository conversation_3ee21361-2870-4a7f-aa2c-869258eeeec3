import httpx
import os
from pathlib import Path


def get_project_root() -> Path:
    """
    Get the absolute path to the project root directory.
    This function finds the project root by looking for key files like requirements.txt and router_config.yaml.
    Works in both local development and container environments.
    """
    import logging

    logger = logging.getLogger(__name__)

    current_path = Path(__file__).resolve()
    logger.debug(f"Current file path: {current_path}")

    # First, try the current working directory (works well in containers)
    cwd = Path.cwd()
    logger.debug(f"Checking current working directory: {cwd}")
    if (cwd / "requirements.txt").exists() and (cwd / "router_config.yaml").exists():
        logger.debug(f"Found project root in cwd: {cwd}")
        return cwd

    # Walk up the directory tree to find the project root
    for parent in current_path.parents:
        logger.debug(f"Checking parent directory: {parent}")
        # Check for key project files that indicate the root directory
        # Look for both requirements.txt and router_config.yaml to ensure we're at the project root
        if (parent / "requirements.txt").exists() and (
            parent / "router_config.yaml"
        ).exists():
            logger.debug(f"Found project root: {parent}")
            return parent

    # Fallback: if we can't find the markers, assume we're in app/utils and go up two levels
    fallback_root = current_path.parent.parent
    logger.debug(f"Using fallback root: {fallback_root}")

    # Final check: if the fallback doesn't have the config files, use cwd anyway
    if not (fallback_root / "router_config.yaml").exists():
        logger.debug(
            f"Fallback root doesn't have config, using cwd as last resort: {cwd}"
        )
        return cwd

    logger.debug(f"Final project root: {fallback_root}")
    return fallback_root


def get_absolute_path(relative_path: str) -> str:
    """
    Convert a relative path to an absolute path based on the project root.

    Args:
        relative_path: Path relative to the project root

    Returns:
        Absolute path as string
    """
    project_root = get_project_root()
    return str(project_root / relative_path)


async def get_async_response(url, auth, params=None):
    async with httpx.AsyncClient() as client:
        # Make the asynchronous request to the external API
        response = await client.get(url, auth=auth, params=params, timeout=None)
        return response


def get_active_window(date):
    year = date.year
    current_month = date.month

    if 0 <= current_month <= 2:
        return f"winter_{year}"
    elif 3 <= current_month <= 8:
        return f"summer_{year}"
    return f"winter_{year + 1}"


def human_readable_number(num):
    if num >= 1_000_000:
        return f"{num / 1_000_000:.1f}m"
    elif num >= 1_000:
        return f"{num // 1_000}k"
    return str(num)


def ordinal_superscript(n):
    suffixes = {1: "ˢᵗ", 2: "ⁿᵈ", 3: "ʳᵈ"}
    if 10 <= n % 100 <= 20:
        suffix = "ᵗʰ"
    else:
        suffix = suffixes.get(n % 10, "ᵗʰ")
    return f"{n}{suffix}"

def snake_to_title(x: str) -> str:
    """
    Convert snake_case to Title Case.
    """
    return " ".join(word.capitalize() for word in str(x).split("_"))