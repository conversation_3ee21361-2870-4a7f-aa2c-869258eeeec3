from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from app.api.whatsapp_llm import router
from app.monitoring.monitoring_endpoints import monitoring_router
from app.monitoring.langfuse_setup import setup_langfuse
from app.config import settings
import logging

logger = logging.getLogger(__name__)

app = FastAPI(
    title=settings.PROJECT_NAME,
    version=settings.VERSION,
    description="A FastAPI service for processing WhatsApp messages via Twilio and OpenAI.",
)

# Initialize database tables (with error handling for Cloud Run)
try:
    from app.db import Base, engine

    if engine:
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created successfully")
    else:
        logger.warning("Database engine not available, skipping table creation")
except Exception as e:
    logger.warning(
        f"Database initialization failed (this may be expected in some environments): {e}"
    )
    # Continue without database - the app can still function for some operations

# CORS settings (if needed)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Adjust this for security (e.g., allow only <PERSON><PERSON><PERSON>)
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize LangFuse monitoring
setup_langfuse()

# Include WhatsApp webhook router
app.include_router(router, prefix="", tags=["whatsapp"])

# Include monitoring router
app.include_router(monitoring_router, prefix="", tags=["monitoring"])


@app.get("/")
async def root():
    return {"message": "WhatsApp Bot API is running!"}
