import time
import logging
import json
from typing import Dict, Any, Optional, List
from contextlib import asynccontextmanager
from langfuse import get_client
from .langfuse_setup import get_langfuse_client

logger = logging.getLogger(__name__)


class WhatsAppTracer:
    """
    Comprehensive tracing system for WhatsApp bot pipeline.

    Traces the entire message processing flow from webhook to response using LangFuse v3.
    """

    def __init__(self):
        self.client = get_client()
        self.active_traces: Dict[str, Any] = {}

    @asynccontextmanager
    async def trace_whatsapp_pipeline(
        self, sender: str, message: str, trace_id: Optional[str] = None
    ):
        """
        Main pipeline tracer - creates a thread for the entire WhatsApp message processing flow.
        Each step will be a separate trace within this thread.

        Args:
            sender: WhatsApp sender identifier
            message: Original message content
            trace_id: Optional custom trace ID
        """
        if not self.client:
            # If LangFuse is not available, provide a no-op context
            yield None
            return

        # Create a unique thread ID for this WhatsApp message processing
        thread_id = trace_id or f"whatsapp_{sender}_{int(time.time())}"
        start_time = time.time()

        try:
            # Start the main pipeline trace using v3 context manager
            with self.client.start_as_current_span(
                name="WhatsApp Message Pipeline",
                input={
                    "sender": sender,
                    "message": message[:200],  # Truncate for privacy
                    "timestamp": start_time,
                },
                metadata={
                    "thread_id": thread_id,
                    "sender": sender,
                    "message_length": len(message),
                    "component": "whatsapp_bot",
                },
            ) as root_span:
                # Set trace attributes for grouping
                root_span.update_trace(
                    session_id=thread_id,  # Use thread_id as session_id for grouping
                    tags=["whatsapp", "pipeline", "main"],
                )

                self.active_traces[thread_id] = {
                    "span": root_span,
                    "thread_id": thread_id,
                    "sender": sender,
                    "start_time": start_time,
                    "steps": [],
                }

                logger.info(f"Started WhatsApp pipeline thread: {thread_id}")

                try:
                    yield thread_id
                finally:
                    # Update final output
                    duration = time.time() - start_time
                    root_span.update(
                        output={"status": "completed", "duration": duration}
                    )
                    logger.info(
                        f"Completed WhatsApp pipeline thread: {thread_id} (duration: {duration:.2f}s)"
                    )

        except Exception as e:
            logger.error(f"Error in WhatsApp pipeline thread: {e}")
            yield None
        finally:
            # Clean up
            if thread_id in self.active_traces:
                del self.active_traces[thread_id]

    async def trace_webhook_received(self, thread_id: str, form_data: Dict[str, Any]):
        """Trace webhook reception and initial processing."""
        if not self.client or thread_id not in self.active_traces:
            return

        execution_time = time.time() - self.active_traces[thread_id]["start_time"]

        # Create span using v3 context manager
        with self.active_traces[thread_id]["span"].start_as_current_span(
            name=f"WhatsApp - Webhook Received - {execution_time:.3f}s",
            input={
                "form_data_keys": list(form_data.keys()),
                "media_count": form_data.get("NumMedia", 0),
            },
            metadata={
                "thread_id": thread_id,
                "execution_time": execution_time,
                "component": "webhook_handler",
            },
        ) as span:
            span.update(
                output={
                    "step": "webhook_received",
                    "processing_success": True,
                }
            )

        return {
            "step": "webhook_received",
            "thread_id": thread_id,
            "form_data_keys": list(form_data.keys()),
            "media_count": form_data.get("NumMedia", 0),
            "timestamp": time.time(),
        }

    async def trace_user_validation(
        self, thread_id: str, phone_number: str, validation_result: bool
    ):
        """Trace user validation process."""
        if not self.client or thread_id not in self.active_traces:
            return

        execution_time = time.time() - self.active_traces[thread_id]["start_time"]

        # Create span using v3 context manager
        with self.active_traces[thread_id]["span"].start_as_current_span(
            name=f"WhatsApp - User Validation - {execution_time:.3f}s",
            input={
                "phone_number": phone_number,
            },
            metadata={
                "thread_id": thread_id,
                "execution_time": execution_time,
                "component": "user_validator",
                "validation_success": validation_result,
            },
        ) as span:
            span.update(
                output={
                    "step": "user_validation",
                    "validation_result": validation_result,
                }
            )

        return {
            "step": "user_validation",
            "thread_id": thread_id,
            "phone_number": phone_number,
            "validation_result": validation_result,
            "timestamp": time.time(),
        }

    async def trace_media_processing(
        self,
        thread_id: str,
        media_type: str,
        media_url: str,
        processing_result: Optional[str],
    ):
        """Trace media processing (images, PDFs, etc.)."""
        if not self.client or thread_id not in self.active_traces:
            return

        execution_time = time.time() - self.active_traces[thread_id]["start_time"]
        processing_success = processing_result is not None

        # Create span using v3 context manager
        with self.active_traces[thread_id]["span"].start_as_current_span(
            name=f"WhatsApp - Media Processing - {execution_time:.3f}s",
            input={
                "media_type": media_type,
                "media_url": media_url[:100],  # Truncate URL
            },
            metadata={
                "thread_id": thread_id,
                "execution_time": execution_time,
                "component": "media_processor",
                "media_type": media_type,
                "processing_success": processing_success,
            },
        ) as span:
            span.update(
                output={
                    "step": "media_processing",
                    "processing_success": processing_success,
                    "processed_content_length": (
                        len(processing_result) if processing_result else 0
                    ),
                }
            )

        return {
            "step": "media_processing",
            "thread_id": thread_id,
            "media_type": media_type,
            "media_url": media_url[:100],  # Truncate URL
            "processing_success": processing_success,
            "processed_content_length": (
                len(processing_result) if processing_result else 0
            ),
            "timestamp": time.time(),
        }

    async def trace_message_buffering(
        self,
        thread_id: str,
        original_message: str,
        final_message: str,
        buffer_delay: float,
    ):
        """Trace message buffering and merging process."""
        if not self.client or thread_id not in self.active_traces:
            return

        execution_time = time.time() - self.active_traces[thread_id]["start_time"]
        messages_merged = original_message != final_message

        # Create span using v3 context manager
        with self.active_traces[thread_id]["span"].start_as_current_span(
            name=f"WhatsApp - Message Buffering - {execution_time:.3f}s",
            input={
                "original_message_length": len(original_message),
                "buffer_delay": buffer_delay,
            },
            metadata={
                "thread_id": thread_id,
                "execution_time": execution_time,
                "component": "message_buffer",
                "messages_merged": messages_merged,
            },
        ) as span:
            span.update(
                output={
                    "step": "message_buffering",
                    "final_message_length": len(final_message),
                    "messages_merged": messages_merged,
                }
            )

        return {
            "step": "message_buffering",
            "thread_id": thread_id,
            "original_message_length": len(original_message),
            "final_message_length": len(final_message),
            "buffer_delay": buffer_delay,
            "messages_merged": messages_merged,
            "timestamp": time.time(),
        }

    async def trace_session_state(
        self, thread_id: str, sender: str, session_state: Dict[str, Any]
    ):
        """Trace session state checking (team selection, confirmation flows)."""
        if not self.client or thread_id not in self.active_traces:
            return

        execution_time = time.time() - self.active_traces[thread_id]["start_time"]
        has_session = bool(session_state)
        awaiting_team_selection = session_state.get("awaiting_team_selection", False)
        awaiting_confirmation = session_state.get("status") == "awaiting_confirmation"

        # Create span using v3 context manager
        with self.active_traces[thread_id]["span"].start_as_current_span(
            name=f"WhatsApp - Session State Check - {execution_time:.3f}s",
            input={
                "sender": sender,
                "has_session": has_session,
            },
            metadata={
                "thread_id": thread_id,
                "execution_time": execution_time,
                "component": "session_manager",
                "session_active": has_session,
            },
        ) as span:
            span.update(
                output={
                    "step": "session_state_check",
                    "awaiting_team_selection": awaiting_team_selection,
                    "awaiting_confirmation": awaiting_confirmation,
                    "stored_requests_count": len(
                        session_state.get("stored_requests", [])
                    ),
                }
            )

        return {
            "step": "session_state_check",
            "thread_id": thread_id,
            "sender": sender,
            "has_session": has_session,
            "awaiting_team_selection": awaiting_team_selection,
            "awaiting_confirmation": awaiting_confirmation,
            "stored_requests_count": len(session_state.get("stored_requests", [])),
            "timestamp": time.time(),
        }

    async def trace_router_classification(
        self,
        thread_id: str,
        message: str,
        function_name: str,
        confidence: float,
        reasoning: str,
        execution_time: float,
    ):
        """Trace router agent classification decision."""
        if not self.client or thread_id not in self.active_traces:
            return

        pipeline_time = time.time() - self.active_traces[thread_id]["start_time"]

        # Create span using v3 context manager
        with self.active_traces[thread_id]["span"].start_as_current_span(
            name=f"WhatsApp - Router Classification - {execution_time:.3f}s",
            input={
                "message_preview": message[:100],
            },
            metadata={
                "thread_id": thread_id,
                "execution_time": execution_time,
                "pipeline_time": pipeline_time,
                "component": "router_agent",
                "function_name": function_name,
                "confidence": confidence,
            },
        ) as span:
            span.update(
                output={
                    "step": "router_classification",
                    "selected_function": function_name,
                    "confidence": confidence,
                    "reasoning": reasoning,
                }
            )

        return {
            "step": "router_classification",
            "thread_id": thread_id,
            "message_preview": message[:100],
            "selected_function": function_name,
            "confidence": confidence,
            "reasoning": reasoning,
            "execution_time": execution_time,
            "timestamp": time.time(),
        }

    async def trace_handler_execution(
        self,
        thread_id: str,
        handler_name: str,
        execution_time: float,
        success: bool,
        error_message: Optional[str] = None,
    ):
        """Trace handler execution (player, team, etc.)."""
        if not self.client or thread_id not in self.active_traces:
            return

        pipeline_time = time.time() - self.active_traces[thread_id]["start_time"]

        # Create span using v3 context manager
        with self.active_traces[thread_id]["span"].start_as_current_span(
            name=f"WhatsApp - Handler Execution - {handler_name} - {execution_time:.3f}s",
            input={
                "handler_name": handler_name,
            },
            metadata={
                "thread_id": thread_id,
                "execution_time": execution_time,
                "pipeline_time": pipeline_time,
                "component": "handler_registry",
                "handler_name": handler_name,
                "success": success,
            },
        ) as span:
            span.update(
                output={
                    "step": "handler_execution",
                    "success": success,
                    "error_message": error_message,
                }
            )

        return {
            "step": "handler_execution",
            "thread_id": thread_id,
            "handler_name": handler_name,
            "execution_time": execution_time,
            "success": success,
            "error_message": error_message,
            "timestamp": time.time(),
        }

    async def trace_openai_assistant(
        self,
        thread_id: str,
        assistant_id: str,
        message: str,
        response_data: Optional[Dict[str, Any]] = None,
        execution_time: Optional[float] = None,
    ):
        """Trace OpenAI Assistant API calls."""
        if not self.client or thread_id not in self.active_traces:
            return

        pipeline_time = time.time() - self.active_traces[thread_id]["start_time"]
        response_received = response_data is not None

        # Create generation using v3 context manager
        with self.active_traces[thread_id]["span"].start_as_current_generation(
            name=f"WhatsApp - OpenAI Assistant Call - {execution_time or 0:.3f}s",
            input={
                "assistant_id": assistant_id,
                "message_length": len(message),
            },
            model=response_data.get("model") if response_data else "unknown",
            metadata={
                "thread_id": thread_id,
                "execution_time": execution_time,
                "pipeline_time": pipeline_time,
                "component": "openai_assistant",
                "assistant_id": assistant_id,
                "response_received": response_received,
            },
        ) as generation:
            generation.update(
                output={
                    "step": "openai_assistant_call",
                    "response_received": response_received,
                    "tokens_used": (
                        response_data.get("tokens_used") if response_data else None
                    ),
                },
                usage_details=(
                    {"total": response_data.get("tokens_used")}
                    if response_data and response_data.get("tokens_used")
                    else None
                ),
            )

        return {
            "step": "openai_assistant_call",
            "thread_id": thread_id,
            "assistant_id": assistant_id,
            "message_length": len(message),
            "response_received": response_received,
            "execution_time": execution_time,
            "timestamp": time.time(),
        }

    async def trace_response_sent(
        self, thread_id: str, sender: str, response_message: str, success: bool
    ):
        """Trace final WhatsApp response sending."""
        if not self.client or thread_id not in self.active_traces:
            return

        pipeline_time = time.time() - self.active_traces[thread_id]["start_time"]

        # Create span using v3 context manager
        with self.active_traces[thread_id]["span"].start_as_current_span(
            name=f"WhatsApp - Response Sent - {pipeline_time:.3f}s",
            input={
                "sender": sender,
                "response_length": len(response_message),
            },
            metadata={
                "thread_id": thread_id,
                "pipeline_time": pipeline_time,
                "component": "whatsapp_sender",
                "success": success,
            },
        ) as span:
            span.update(
                output={
                    "step": "whatsapp_response_sent",
                    "success": success,
                }
            )

        return {
            "step": "whatsapp_response_sent",
            "thread_id": thread_id,
            "sender": sender,
            "response_length": len(response_message),
            "success": success,
            "timestamp": time.time(),
        }

    async def trace_error(
        self, thread_id: str, error_type: str, error_message: str, step: str
    ):
        """Trace errors that occur during processing."""
        if not self.client or thread_id not in self.active_traces:
            return

        pipeline_time = time.time() - self.active_traces[thread_id]["start_time"]

        # Create span for error handling using v3 context manager
        with self.active_traces[thread_id]["span"].start_as_current_span(
            name=f"WhatsApp - Error Handling - {pipeline_time:.3f}s",
            input={
                "failed_step": step,
                "error_type": error_type,
            },
            metadata={
                "thread_id": thread_id,
                "pipeline_time": pipeline_time,
                "component": "error_handler",
                "error_type": error_type,
                "failed_step": step,
            },
        ) as span:
            span.update(
                output={
                    "step": "error_handling",
                    "error_message": error_message,
                },
                level="ERROR",
            )

        return {
            "step": "error_handling",
            "thread_id": thread_id,
            "error_type": error_type,
            "error_message": error_message,
            "failed_step": step,
            "timestamp": time.time(),
        }

    def get_trace_summary(self, thread_id: str) -> Optional[Dict[str, Any]]:
        """Get summary of an active thread."""
        if thread_id not in self.active_traces:
            return None

        trace_info = self.active_traces[thread_id]
        return {
            "thread_id": thread_id,
            "sender": trace_info["sender"],
            "start_time": trace_info["start_time"],
            "duration": time.time() - trace_info["start_time"],
            "steps_completed": len(trace_info["steps"]),
            "status": "active",
        }


# Global tracer instance
whatsapp_tracer = WhatsAppTracer()


# Convenience functions for easy usage
def start_whatsapp_trace(sender: str, message: str):
    """Start a new WhatsApp pipeline thread."""
    return whatsapp_tracer.trace_whatsapp_pipeline(sender, message)


async def trace_webhook(thread_id: str, form_data: Dict[str, Any]):
    """Trace webhook reception."""
    return await whatsapp_tracer.trace_webhook_received(thread_id, form_data)


async def trace_validation(thread_id: str, phone_number: str, result: bool):
    """Trace user validation."""
    return await whatsapp_tracer.trace_user_validation(thread_id, phone_number, result)


async def trace_media(
    thread_id: str, media_type: str, media_url: str, result: Optional[str]
):
    """Trace media processing."""
    return await whatsapp_tracer.trace_media_processing(
        thread_id, media_type, media_url, result
    )


async def trace_buffering(thread_id: str, original: str, final: str, delay: float):
    """Trace message buffering."""
    return await whatsapp_tracer.trace_message_buffering(
        thread_id, original, final, delay
    )


async def trace_session(thread_id: str, sender: str, session: Dict[str, Any]):
    """Trace session state."""
    return await whatsapp_tracer.trace_session_state(thread_id, sender, session)


async def trace_router(
    thread_id: str,
    message: str,
    function: str,
    confidence: float,
    reasoning: str,
    time: float,
):
    """Trace router decision."""
    return await whatsapp_tracer.trace_router_classification(
        thread_id, message, function, confidence, reasoning, time
    )


async def trace_handler(
    thread_id: str,
    handler: str,
    time: float,
    success: bool,
    error: Optional[str] = None,
):
    """Trace handler execution."""
    return await whatsapp_tracer.trace_handler_execution(
        thread_id, handler, time, success, error
    )


async def trace_assistant(
    thread_id: str,
    assistant_id: str,
    message: str,
    response: Optional[Dict] = None,
    time: Optional[float] = None,
):
    """Trace OpenAI assistant call."""
    return await whatsapp_tracer.trace_openai_assistant(
        thread_id, assistant_id, message, response, time
    )


async def trace_response(thread_id: str, sender: str, response: str, success: bool):
    """Trace WhatsApp response."""
    return await whatsapp_tracer.trace_response_sent(
        thread_id, sender, response, success
    )


async def trace_error(thread_id: str, error_type: str, error_msg: str, step: str):
    """Trace error occurrence."""
    return await whatsapp_tracer.trace_error(thread_id, error_type, error_msg, step)
