"""
Secure HTTP client for WhatsApp bot backend API calls.
Handles API key authentication, rate limiting, and error handling.
"""

import time
import logging
import asyncio
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass
from enum import Enum
import requests
import httpx
from langfuse import observe

from app.config import settings
from app.utils.whatsapp_handler import send_whatsapp_message

logger = logging.getLogger(__name__)


class APIError(Exception):
    """Custom exception for API-related errors"""

    def __init__(
        self, status_code: int, message: str, response_data: Optional[Dict] = None
    ):
        self.status_code = status_code
        self.message = message
        self.response_data = response_data or {}
        super().__init__(f"API Error {status_code}: {message}")


class RateLimitError(APIError):
    """Exception for rate limiting errors"""

    def __init__(self, retry_after: Optional[int] = None):
        self.retry_after = retry_after
        super().__init__(429, "Rate limit exceeded", {"retry_after": retry_after})


@dataclass
class APIResponse:
    """Standardized API response wrapper"""

    status_code: int
    data: Optional[Dict[str, Any]]
    success: bool
    error_message: Optional[str] = None
    retry_after: Optional[int] = None


class SecureHTTPClient:
    """
    Secure HTTP client for WhatsApp bot backend API calls.
    Handles authentication, rate limiting, and error handling.
    """

    def __init__(self, base_url: str = None, api_key: str = None):
        self.base_url = base_url or settings.PLATFORM_URL
        self.api_key = api_key or settings.WHATSAPP_BOT_API_KEY
        self.max_retries = 3
        self.base_delay = 1.0  # Base delay for exponential backoff
        self.max_delay = 60.0  # Maximum delay between retries

        if not self.api_key:
            logger.error("WHATSAPP_BOT_API_KEY not configured")
            raise ValueError("API key is required for secure HTTP client")

    def _get_headers(
        self, additional_headers: Optional[Dict[str, str]] = None
    ) -> Dict[str, str]:
        """Get headers with API key authentication"""
        headers = {
            "X-API-Key": self.api_key,
            "Content-Type": "application/json",
        }
        if additional_headers:
            headers.update(additional_headers)
        return headers

    def _calculate_backoff_delay(
        self, attempt: int, retry_after: Optional[int] = None
    ) -> float:
        """Calculate exponential backoff delay"""
        if retry_after:
            return min(retry_after, self.max_delay)

        delay = self.base_delay * (2**attempt)
        return min(delay, self.max_delay)

    def _handle_error_response(
        self, response: requests.Response, sender: Optional[str] = None
    ) -> APIResponse:
        """Handle error responses and send appropriate WhatsApp messages"""
        try:
            error_data = response.json()
        except:
            error_data = {}

        error_message = error_data.get("detail", f"HTTP {response.status_code}")

        # Create LangFuse trace for security events
        from langfuse import get_client

        langfuse = get_client()

        if response.status_code == 401:
            # Unauthorized - Invalid/missing API key
            user_message = (
                "🔒 Authentication error. Please contact support for assistance."
            )
            logger.error(f"API authentication failed: Invalid or missing API key")

            # Log security event to LangFuse
            with langfuse.start_as_current_generation(
                name="Security Alert - Authentication Failed",
                input={"sender": sender, "status_code": 401},
                metadata={
                    "security_event": "authentication_failed",
                    "sender": sender,
                    "error_detail": error_message,
                    "severity": "high",
                },
            ) as span:
                span.update(
                    output={
                        "alert": "API key authentication failed",
                        "action": "blocked_request",
                    },
                    level="ERROR",
                )

        elif response.status_code == 403:
            # Forbidden - Phone number not authorized
            user_message = "🚫 Your phone number is not authorized for this service. Please contact support."
            logger.warning(f"Phone number not authorized: {sender}")

            # Log security event to LangFuse
            with langfuse.start_as_current_generation(
                name="Security Alert - Unauthorized Access",
                input={"sender": sender, "status_code": 403},
                metadata={
                    "security_event": "unauthorized_access",
                    "sender": sender,
                    "error_detail": error_message,
                    "severity": "medium",
                },
            ) as span:
                span.update(
                    output={
                        "alert": "Phone number not authorized",
                        "action": "blocked_request",
                    },
                    level="WARNING",
                )

        elif response.status_code == 429:
            # Rate limit exceeded
            retry_after = response.headers.get("Retry-After")
            retry_after_int = int(retry_after) if retry_after else None

            if retry_after_int and retry_after_int < 60:
                user_message = f"⏳ Too many requests. Please wait {retry_after_int} seconds and try again."
            else:
                user_message = (
                    "⏳ Too many requests. Please wait a moment and try again."
                )

            logger.warning(
                f"Rate limit exceeded for {sender}, retry after: {retry_after}"
            )

            # Log rate limiting event to LangFuse
            with langfuse.start_as_current_generation(
                name="Rate Limit Event",
                input={
                    "sender": sender,
                    "status_code": 429,
                    "retry_after": retry_after_int,
                },
                metadata={
                    "security_event": "rate_limit_exceeded",
                    "sender": sender,
                    "retry_after": retry_after_int,
                    "severity": "low",
                },
            ) as span:
                span.update(
                    output={
                        "alert": "Rate limit exceeded",
                        "action": "throttled_request",
                    },
                    level="WARNING",
                )

            return APIResponse(
                status_code=response.status_code,
                data=error_data,
                success=False,
                error_message=error_message,
                retry_after=retry_after_int,
            )
        elif response.status_code == 409:
            # Conflict error
            pass

        else:
            # Other errors
            user_message = "⚠️ Service temporarily unavailable. Please try again later."
            logger.error(f"API error {response.status_code}: {error_message}")

        # Send user-friendly message to WhatsApp
        if sender:
            send_whatsapp_message(sender, user_message)

        return APIResponse(
            status_code=response.status_code,
            data=error_data,
            success=False,
            error_message=error_message,
        )

    @observe(name="Secure HTTP Request", as_type="generation")
    def request(
        self,
        method: str,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None,
        json_data: Optional[Union[Dict[str, Any], list]] = None,
        sender: Optional[str] = None,
        timeout: int = 15,
    ) -> APIResponse:
        """
        Make a secure HTTP request with retry logic and error handling

        Args:
            method: HTTP method (GET, POST, PUT, etc.)
            endpoint: API endpoint (e.g., "/player_records/whatsapp")
            params: Query parameters
            json_data: JSON payload for POST/PUT requests
            sender: WhatsApp sender for error messaging
            timeout: Request timeout in seconds

        Returns:
            APIResponse object with standardized response data
        """
        url = f"{self.base_url}{endpoint}"
        headers = self._get_headers()
        request_start_time = time.time()

        last_error = None

        for attempt in range(self.max_retries):
            try:
                logger.info(
                    f"Making {method} request to {endpoint} (attempt {attempt + 1})"
                )

                response = requests.request(
                    method=method,
                    url=url,
                    params=params,
                    json=json_data,
                    headers=headers,
                    timeout=timeout,
                )

                # Handle successful responses
                if response.status_code < 400:
                    try:
                        data = response.json()
                    except:
                        data = {}

                    # Log successful request timing
                    request_duration = time.time() - request_start_time
                    logger.info(
                        f"✅ {method} {endpoint} completed successfully in {request_duration:.3f}s"
                    )

                    return APIResponse(
                        status_code=response.status_code, data=data, success=True
                    )

                # Handle rate limiting with retry
                elif response.status_code == 429:
                    api_response = self._handle_error_response(response, sender)

                    # Only retry on rate limiting if we have attempts left
                    if attempt < self.max_retries - 1:
                        delay = self._calculate_backoff_delay(
                            attempt, api_response.retry_after
                        )
                        logger.info(f"Rate limited, retrying in {delay} seconds")
                        time.sleep(delay)
                        continue

                    return api_response

                # Handle other errors (don't retry auth/permission errors)
                else:
                    return self._handle_error_response(response, sender)

            except requests.RequestException as e:
                last_error = e
                logger.warning(f"Request failed (attempt {attempt + 1}): {e}")

                if attempt < self.max_retries - 1:
                    delay = self._calculate_backoff_delay(attempt)
                    logger.info(f"Retrying in {delay} seconds")
                    time.sleep(delay)
                    continue

        # All retries exhausted
        error_message = (
            f"Connection failed after {self.max_retries} attempts: {last_error}"
        )
        logger.error(error_message)

        if sender:
            send_whatsapp_message(
                sender, "🔌 Connection error. Please try again later."
            )

        return APIResponse(
            status_code=0, data=None, success=False, error_message=error_message
        )

    def get(
        self,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None,
        sender: Optional[str] = None,
        **kwargs,
    ) -> APIResponse:
        """Make a GET request"""
        return self.request("GET", endpoint, params=params, sender=sender, **kwargs)

    def post(
        self,
        endpoint: str,
        json_data: Optional[Union[Dict[str, Any], list]] = None,
        params: Optional[Dict[str, Any]] = None,
        sender: Optional[str] = None,
        **kwargs,
    ) -> APIResponse:
        """Make a POST request"""
        return self.request(
            "POST",
            endpoint,
            params=params,
            json_data=json_data,
            sender=sender,
            **kwargs,
        )

    def put(
        self,
        endpoint: str,
        json_data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None,
        sender: Optional[str] = None,
        **kwargs,
    ) -> APIResponse:
        """Make a PUT request"""
        return self.request(
            "PUT", endpoint, params=params, json_data=json_data, sender=sender, **kwargs
        )


# Global instance for easy access
_secure_client = None


def get_secure_http_client() -> SecureHTTPClient:
    """Get the global secure HTTP client instance"""
    global _secure_client
    if _secure_client is None:
        _secure_client = SecureHTTPClient()
    return _secure_client
