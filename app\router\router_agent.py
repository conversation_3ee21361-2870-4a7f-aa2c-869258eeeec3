import yaml
import json
import logging
import os
import time
from typing import Dict, Any, Optional, List
from langfuse import observe
from app.config import settings
from app.utils.ai_client import get_ai_client, AIProvider
from app.utils import get_absolute_path
import os

logger = logging.getLogger(__name__)


# Import performance monitor (with lazy loading to avoid circular imports)
def get_performance_monitor():
    from app.monitoring.performance_monitor import performance_monitor

    return performance_monitor


class RouterAgent:
    """
    Router Agent that uses Gemini 2.5 Flash Lite Preview with OpenAI fallback for function calling
    to classify WhatsApp messages and route them to appropriate handlers.
    """

    def __init__(self, config_path: str = "router_config.yaml"):
        """Initialize the router agent with configuration."""
        self.ai_client = get_ai_client()
        self.config = self._load_config(config_path)
        self.functions = self._build_function_definitions()
        self.system_prompt = self.config["router"]["system_prompt"]

        # Router settings
        self.model = self.config["router"]["model"]
        self.temperature = self.config["router"]["temperature"]
        self.max_tokens = self.config["router"]["max_tokens"]
        self.primary_provider = self.config["router"].get("primary_provider", "gemini")
        self.fallback_provider = self.config["router"].get(
            "fallback_provider", "openai"
        )

        logger.info(f"RouterAgent initialized with {len(self.functions)} functions")
        logger.info(
            f"Primary provider: {self.primary_provider}, Fallback: {self.fallback_provider}"
        )

        # Log available providers
        available_providers = self.ai_client.get_available_providers()
        logger.info(f"Available AI providers: {[p.value for p in available_providers]}")

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load router configuration from YAML file."""
        try:
            # Convert to absolute path if it's a relative path
            if not os.path.isabs(config_path):
                absolute_config_path = get_absolute_path(config_path)
            else:
                absolute_config_path = config_path

            logger.info(f"Loading router configuration from: {absolute_config_path}")

            with open(absolute_config_path, "r", encoding="utf-8") as file:
                config = yaml.safe_load(file)
                logger.info(f"Successfully loaded router configuration")
                return config
        except FileNotFoundError:
            logger.error(f"Router config file not found: {config_path}")
            logger.error(f"Absolute path attempted: {absolute_config_path}")
            logger.error(f"Current working directory: {os.getcwd()}")
            raise
        except yaml.YAMLError as e:
            logger.error(f"Error parsing router config: {e}")
            raise

    def _build_function_definitions(self) -> List[Dict[str, Any]]:
        """Build OpenAI function definitions from config."""
        functions = []

        # Add enabled handler functions
        for handler_key, handler_config in self.config["handlers"].items():
            if handler_config.get("enabled", False):
                # Find corresponding function definition
                for func_def in self.config["functions"]:
                    if func_def["name"] == handler_config["function_name"]:
                        functions.append(func_def)
                        break

        # Always add unclassified message handler
        for func_def in self.config["functions"]:
            if func_def["name"] == "handle_unclassified_message":
                functions.append(func_def)
                break

        logger.info(f"Built {len(functions)} function definitions")
        return functions

    async def route_message(self, message: str, sender: str) -> Dict[str, Any]:
        """
        Route a message to the appropriate handler using Gemini/OpenAI function calling.

        Args:
            message: The user's WhatsApp message
            sender: The sender identifier

        Returns:
            Dict containing routing result with handler info and parameters
        """
        start_time = time.perf_counter()

        try:
            logger.info(f"Routing message from {sender}: {message[:100]}...")

            # Prepare the prompt with message context
            user_prompt = f"""
            Analyze this WhatsApp message and determine which handler should process it:

            Message: "{message}"
            Sender: {sender}

            Choose the most appropriate function to call based on the message content.

            IMPORTANT: When calling any function, you MUST include both 'message' and 'sender' parameters exactly as provided above.
            - message: "{message}"
            - sender: {sender}
            """

            # Use LangFuse context manager for proper usage tracking
            from langfuse import get_client

            langfuse = get_client()

            with langfuse.start_as_current_generation(
                name="Router Classification",
                model=self.model,
                input={
                    "message_preview": message[:100],
                    "sender": sender,
                    "system_prompt": self.system_prompt[:200],
                },
                metadata={"component": "router_agent", "step": "classification"},
            ) as router_span:
                # Make AI API call with function calling using unified client
                ai_response = await self.ai_client.chat_completion(
                    messages=[
                        {"role": "system", "content": self.system_prompt},
                        {"role": "user", "content": user_prompt},
                    ],
                    model=self.model,
                    functions=self.functions,
                    temperature=self.temperature,
                    max_tokens=self.max_tokens,
                )

                # Process the response
                if ai_response.function_call:
                    function_name = ai_response.function_call["name"]
                    function_args = ai_response.function_call["arguments"]

                    # Log the routing decision
                    logger.info(
                        f"Routed to {function_name} with confidence: {function_args.get('confidence', 'N/A')} using {ai_response.provider.value}"
                    )

                    # Track routing decision for monitoring
                    execution_time = time.perf_counter() - start_time

                    # Update LangFuse span with results and usage information
                    router_span.update(
                        output={
                            "function_name": function_name,
                            "function_args": function_args,
                            "confidence": function_args.get("confidence", 0.0),
                            "reasoning": function_args.get("reasoning", ""),
                            "provider_used": ai_response.provider.value,
                            "execution_time": execution_time,
                        },
                        usage_details={
                            "input_tokens": (
                                ai_response.usage["prompt_tokens"]
                                if ai_response.usage
                                else 0
                            ),
                            "output_tokens": (
                                ai_response.usage["completion_tokens"]
                                if ai_response.usage
                                else 0
                            ),
                            "total_tokens": (
                                ai_response.usage["total_tokens"]
                                if ai_response.usage
                                else 0
                            ),
                        },
                    )

                    if self.config["monitoring"]["log_routing_decisions"]:
                        self._log_routing_decision(
                            message, sender, function_name, function_args
                        )

                    # Track performance metrics
                    try:
                        monitor = get_performance_monitor()
                        monitor.track_router_decision(
                            sender=sender,
                            message=message,
                            function_name=function_name,
                            confidence=function_args.get("confidence", 0.0),
                            reasoning=function_args.get("reasoning", ""),
                            execution_time=execution_time,
                            success=True,
                        )
                    except Exception as e:
                        logger.warning(f"Failed to track router decision: {e}")

                    return {
                        "success": True,
                        "function_name": function_name,
                        "function_args": function_args,
                        "handler": self._get_handler_from_function(function_name),
                        "raw_response": ai_response.raw_response,
                        "execution_time": execution_time,
                        "provider_used": ai_response.provider.value,
                        "model_used": ai_response.model,
                        "usage": ai_response.usage,
                    }
                else:
                    # Fallback if no function was called
                    router_span.update(
                        output={"error": "No function called"},
                        usage_details={
                            "input_tokens": (
                                ai_response.usage["prompt_tokens"]
                                if ai_response.usage
                                else 0
                            ),
                            "output_tokens": (
                                ai_response.usage["completion_tokens"]
                                if ai_response.usage
                                else 0
                            ),
                            "total_tokens": (
                                ai_response.usage["total_tokens"]
                                if ai_response.usage
                                else 0
                            ),
                        },
                    )
                    logger.warning(
                        f"No function called for message from {sender} using {ai_response.provider.value}"
                    )
                    return self._handle_no_function_call(message, sender)

        except Exception as e:
            logger.error(f"Error routing message from {sender}: {str(e)}")
            return self._handle_routing_error(message, sender, str(e))

    def _get_handler_from_function(self, function_name: str) -> Optional[str]:
        """Get handler name from function name."""
        for handler_key, handler_config in self.config["handlers"].items():
            if handler_config["function_name"] == function_name:
                return handler_key

        if function_name == "handle_unclassified_message":
            return "unclassified"

        return None

    def _log_routing_decision(
        self,
        message: str,
        sender: str,
        function_name: str,
        function_args: Dict[str, Any],
    ):
        """Log routing decision for monitoring and analysis."""
        log_data = {
            "sender": sender,
            "message_preview": message[:100],
            "function_name": function_name,
            "confidence": function_args.get("confidence"),
            "reasoning": function_args.get("reasoning"),
            "timestamp": (
                logger.handlers[0].formatter.formatTime(
                    logging.LogRecord("", 0, "", 0, "", (), None)
                )
                if logger.handlers
                else None
            ),
        }

        # This could be enhanced to send to external monitoring service
        logger.info(f"Routing decision: {json.dumps(log_data)}")

    def _handle_no_function_call(self, message: str, sender: str) -> Dict[str, Any]:
        """Handle case where OpenAI didn't call any function."""
        return {
            "success": False,
            "error_type": "no_function_call",
            "error_message": self.config["error_handling"]["default_message"],
            "function_name": "handle_unclassified_message",
            "function_args": {
                "message": message,
                "sender": sender,
                "message_type": "unclear",
                "suggestions": [
                    "Please rephrase your message",
                    "Contact support for assistance",
                ],
            },
            "handler": "unclassified",
        }

    def _handle_routing_error(
        self, message: str, sender: str, error: str
    ) -> Dict[str, Any]:
        """Handle routing errors gracefully."""
        return {
            "success": False,
            "error_type": "routing_error",
            "error_message": self.config["error_handling"]["default_message"],
            "error_details": error,
            "function_name": "handle_unclassified_message",
            "function_args": {
                "message": message,
                "sender": sender,
                "message_type": "error",
                "suggestions": [
                    "Please try again",
                    "Contact support if the issue persists",
                ],
            },
            "handler": "unclassified",
        }

    def get_enabled_handlers(self) -> List[str]:
        """Get list of currently enabled handlers."""
        return [
            handler_key
            for handler_key, handler_config in self.config["handlers"].items()
            if handler_config.get("enabled", False)
        ]

    def reload_config(self, config_path: str = "router_config.yaml"):
        """Reload configuration (useful for dynamic updates)."""
        self.config = self._load_config(config_path)
        self.functions = self._build_function_definitions()
        self.system_prompt = self.config["router"]["system_prompt"]
        logger.info("Router configuration reloaded")
