router:
  primary_provider: "gemini"
  fallback_provider: "openai"
  model: "gemini-2.5-flash-lite-preview-06-17"
  temperature: 0.1
  max_tokens: 10000
  system_prompt: |
    You are a WhatsApp assistant message router. Your job is to analyze each incoming message and choose exactly one handler:

    ## COMMAND-BASED ROUTING (HIGHEST PRIORITY)
    For maximum efficiency, users can use specific commands that guarantee correct routing:

    1. **Player Handler** (route_to_player_handler)
       - **COMMAND TRIGGERS** (Confidence: 1.0):
         - "Create player" (or similar: "create player", "add player", "new player")
         - Contains Transfermarkt URL
       - **AUTO TRIGGERS**:
         - Message matches regex: `/https?:\/\/(?:www\.)?transfermarkt\.com\/[A-Za-z0-9\-\_\/]+/`
       - Confidence: 1.0
       - Reasoning: "Command 'Create player' detected" OR "Found a Transfermarkt URL via regex"

    2. **Player Update Handler** (route_to_player_update_handler)
       - **COMMAND TRIGGERS** (Confidence: 1.0):
         - "Update player" (or similar: "update player", "modify player", "edit player")
       - **AUTO TRIGGERS**:
         - Contains update keywords (update, change, modify, edit, set) AND player name
         - Message is about updating existing player information
         - Contains player name AND update-related context (salary, position, status, etc.)
         - Mentions changing player details, control stage, or assignment
         - Does NOT contain Transfermarkt URLs (those go to player handler)
       - Confidence: 1.0 for commands, 0.7-1.0 for auto triggers
       - Reasoning: "Command 'Update player' detected" OR "Update keyword 'change' + player name 'John Smith' found"

    3. **Activity Handler** (route_to_activity_handler)
       - **COMMAND TRIGGERS** (Confidence: 1.0):
         - "Create task" (or similar: "create task", "add task", "new task")
         - "Create deal" (or similar: "create deal", "add deal", "new deal")
       - **AUTO TRIGGERS**:
         - Contains activity keywords (offer, remind, follow up, send, prepare, meet, call, contact, schedule, arrange, negotiate, talk to, discuss, propose, submit, deliver, create, organize)
         - Message is about creating tasks or deals
         - Contains action verbs with player/club context (e.g., "call Markus about Smolenski", "offer to Barcelona", "meet with agent")
         - Mentions deal-related activities (offered, need to offer, they called, negotiating, they accepted)
         - Contains task-related activities (send documents, prepare contracts, schedule meetings)
         - Does NOT contain Transfermarkt URLs or team building requests
       - Confidence: 1.0 for commands, 0.8-1.0 for auto triggers
       - Reasoning: "Command 'Create task' detected" OR "Activity keyword 'offer' + player/club context found"

    4. **Team Handler** (route_to_team_handler)
       - **NO COMMAND TRIGGERS** (Team requests work automatically)
       - **AUTO TRIGGERS**:
         - Contains team name AND position(s) - for team building requests
         - Contains team name AND player profiles/descriptions AND player position - for team analysis
         - Contains football positions (GK, CB, LCB, RCB, WB, 6, 8, 9, CMF, RW, LW, STRIKER, GOALKEEPER, CENTRAL DEFENDER, RIGHT WINGBACK, CENTRAL MIDFIELDER, WINGER, ATTACKING MIDFIELDER, etc.) AND team context
         - Contains player requirements/profiles for a specific team. Must contain position and a team name
         - Message is about wanting players for specific positions
         - Contains transfer-related keywords (TRANSFER TARGETS, TRANSFER WINDOW, looking for players, need players). It must contain team name and position/s
         - Contains detailed player specifications or requirements for a team. Must include position
         - Long messages with team names and multiple position descriptions
       - Confidence: 0.1-1.0 (depending on signal strength)
       - Reasoning: "Team name 'Levski' + position 'rw' found"

    5. **Unclassified** (handle_unclassified_message)
       - Trigger: anything else
       - Provide best-guess `message_type` (e.g. "greeting", "question"), and two rephrase suggestions:
         - "Try including a Transfermarkt URL or use 'Create player' command"
         - "Use commands like 'Update player', 'Create task', or specify team name with position"

    ## Examples
    - "Create player https://www.transfermarkt.com/john-doe/profil/spieler/12345"
      → route_to_player_handler (Command detected)
    - "Check out his profile: https://www.transfermarkt.com/john-doe/profil/spieler/12345"
      → route_to_player_handler (Auto trigger)
    - "Update player John Smith salary to 120k"
      → route_to_player_update_handler (Command detected)
    - "Change David Rodriguez position to CAM, assigned to Maria Garcia"
      → route_to_player_update_handler (Auto trigger)
    - "Create task: Call Markus about Smolenski tomorrow"
      → route_to_activity_handler (Command detected)
    - "I offered Smolenski to Watford for €500k"
      → route_to_activity_handler (Auto trigger)
    - "Create deal with Barcelona for €2M"
      → route_to_activity_handler (Command detected)
    - "Levski needs a CMF and a RW, budget 1.2m"
      → route_to_team_handler (Auto trigger)
    - "Hey, what's up?"
      → handle_unclassified_message
    - "looking for a striker, 26 years old, 5 million transfer fee"
      → handle_unclassified_message

handlers:
  player_handler:
    name: "Player Request Handler"
    description: "Handles player requests with Transfermarkt URLs and creation commands"
    function_name: "route_to_player_handler"
    enabled: true
    keywords: ["transfermarkt", "player", "profile", "goals", "assists", "create player", "add player", "new player"]

  team_handler:
    name: "Team Request Handler"
    description: "Handles team requests"
    function_name: "route_to_team_handler"
    enabled: true
    keywords: ["team", "position", "salary", "transfer fee", "tf", "contact", "pay", "need", "looking", "transfer targets", "transfer window", "striker", "goalkeeper", "central defender", "wingback", "central midfielder", "winger", "attacking midfielder"]

  player_update_handler:
    name: "Player Update Handler"
    description: "Handles player record updates and modifications"
    function_name: "route_to_player_update_handler"
    enabled: true
    keywords: ["update", "change", "modify", "edit", "set", "assign", "status", "salary", "position", "stage", "contact", "video", "description", "price", "update player", "modify player", "edit player"]

  activity_handler:
    name: "Activity Task Handler"
    description: "Handles football activity task creation and management"
    function_name: "route_to_activity_handler"
    enabled: true
    keywords: ["offer", "offered", "remind", "follow up", "send", "prepare", "meet", "call", "contact", "schedule", "arrange", "negotiate", "talk to", "discuss", "propose", "submit", "deliver", "create", "organize", "deal", "task", "activity", "tomorrow", "next week", "friday", "monday", "deadline", "due", "commission", "contract", "video", "document", "create task", "add task", "new task", "create deal", "add deal", "new deal"]

  statistics_handler:
    name: "Statistics Request Handler"
    description: "Handles football statistics and data requests"
    function_name: "route_to_statistics_handler"
    enabled: false  # Will be implemented later
    keywords: ["stats", "statistics", "data", "performance", "metrics"]

  report_handler:
    name: "Team Report Handler"
    description: "Handles team report generation requests"
    function_name: "route_to_report_handler"
    enabled: false  # Will be implemented later
    keywords: ["report", "summary", "analysis", "overview"]

error_handling:
  default_message: "I couldn't understand your request. Please try rephrasing your message or contact support for assistance."
  log_unclassified: true
  max_retries: 2

monitoring:
  langfuse_enabled: true
  log_performance: true
  track_handler_usage: true
  log_routing_decisions: true

# Function definitions for OpenAI function calling
functions:
  - name: "route_to_player_handler"
    description: "Route message to player request handler for Transfermarkt URLs and player creation commands"
    parameters:
      type: "object"
      properties:
        message:
          type: "string"
          description: "The original user message to process"
        sender:
          type: "string"
          description: "The sender identifier (WhatsApp number)"
        confidence:
          type: "number"
          description: "Confidence score (0-1) for this routing decision"
        reasoning:
          type: "string"
          description: "Brief explanation of why this handler was chosen"
      required: ["message", "sender", "confidence", "reasoning"]

  - name: "route_to_team_handler"
    description: "Route message to team request handler for team building"
    parameters:
      type: "object"
      properties:
        message:
          type: "string"
          description: "The original user message to process"
        sender:
          type: "string"
          description: "The sender identifier (WhatsApp number)"
        confidence:
          type: "number"
          description: "Confidence score (0-1) for this routing decision"
        reasoning:
          type: "string"
          description: "Brief explanation of why this handler was chosen"
      required: ["message", "sender", "confidence", "reasoning"]

  - name: "route_to_player_update_handler"
    description: "Route message to player update handler for modifying existing player records and update commands"
    parameters:
      type: "object"
      properties:
        message:
          type: "string"
          description: "The original user message to process"
        sender:
          type: "string"
          description: "The sender identifier (WhatsApp number)"
        confidence:
          type: "number"
          description: "Confidence score (0-1) for this routing decision"
        reasoning:
          type: "string"
          description: "Brief explanation of why this handler was chosen"
      required: ["message", "sender", "confidence", "reasoning"]

  - name: "route_to_activity_handler"
    description: "Route message to activity handler for task and deal creation commands"
    parameters:
      type: "object"
      properties:
        message:
          type: "string"
          description: "The original user message to process"
        sender:
          type: "string"
          description: "The sender identifier (WhatsApp number)"
        confidence:
          type: "number"
          description: "Confidence score (0-1) for this routing decision"
        reasoning:
          type: "string"
          description: "Brief explanation of why this handler was chosen"
      required: ["message", "sender", "confidence", "reasoning"]

  - name: "handle_unclassified_message"
    description: "Handle messages that don't fit into any specific category"
    parameters:
      type: "object"
      properties:
        message:
          type: "string"
          description: "The original user message that couldn't be classified"
        sender:
          type: "string"
          description: "The sender identifier (WhatsApp number)"
        message_type:
          type: "string"
          description: "Best guess at what type of message this might be"
        suggestions:
          type: "array"
          items:
            type: "string"
          description: "Two helpful suggestions for rephrasing the message"
      required: ["message", "sender", "message_type", "suggestions"]
