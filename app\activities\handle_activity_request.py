import re
import json
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from langfuse import observe
from app.config import settings
from app.db import insert_whatsapp_request
from app.monitoring.whatsapp_tracer import trace_assistant, trace_response, trace_error
from app.utils.ai_client import get_ai_client
from app.utils import get_absolute_path, get_async_response
from app.utils.team_selection import select_best_team_match, format_team_display_name
from app.utils.contact_lookup import lookup_contact, format_contact_for_activity
from app.utils.whatsapp_handler import (
    send_whatsapp_message,
    send_button_whatsapp_message,
)
from app.utils.secure_http_client import get_secure_http_client

logger = logging.getLogger(__name__)

# Export functions for use in main webhook
__all__ = [
    "process_activity_request",
    "handle_activity_confirmation",
    "process_next_activity",
    "handle_activity_player_selection",
    "handle_activity_team_selection",
]


def _load_activity_instructions():
    """Load activity extraction instructions from file."""
    try:
        instructions_path = get_absolute_path(
            "app/activities/activity_instructions.txt"
        )
        with open(instructions_path, "r", encoding="utf-8") as f:
            return f.read().strip()
    except FileNotFoundError:
        raise FileNotFoundError("activity_instructions.txt not found")


def validate_and_fix_dates(activity: Dict[str, Any]) -> Dict[str, Any]:
    """Validate and fix date formats in activity data"""
    date_fields = ["due_date", "next_action", "date_deal_created"]

    for field in date_fields:
        if field in activity and activity[field]:
            date_value = activity[field]

            # Skip if already None or empty
            if not date_value:
                continue

            # If it's already a proper date string (YYYY-MM-DD), keep it
            if isinstance(date_value, str) and re.match(
                r"^\d{4}-\d{2}-\d{2}$", date_value
            ):
                continue

            # Try to parse and reformat various date formats
            try:
                if isinstance(date_value, str):
                    # Handle common date formats
                    for fmt in [
                        "%Y-%m-%d",
                        "%d/%m/%Y",
                        "%m/%d/%Y",
                        "%Y-%m-%d %H:%M:%S",
                    ]:
                        try:
                            parsed_date = datetime.strptime(date_value, fmt)
                            activity[field] = parsed_date.strftime("%Y-%m-%d")
                            logger.info(
                                f"✅ Fixed date format for {field}: '{date_value}' -> '{activity[field]}'"
                            )
                            break
                        except ValueError:
                            continue
                    else:
                        # If no format matched, set to None
                        logger.warning(
                            f"⚠️ Invalid date format for {field}: '{date_value}', setting to null"
                        )
                        activity[field] = None

            except Exception as e:
                logger.warning(
                    f"⚠️ Error processing date field {field}: {e}, setting to null"
                )
                activity[field] = None

    return activity


@observe(name="Activity Handler", as_type="generation")
async def process_activity_request(
    sender: str, message: str, client, user_sessions: dict, trace_id: str = None
):
    """Handle activity request processing using AI extraction"""
    try:
        # Log the request
        try:
            insert_whatsapp_request(sender, message)
        except Exception as e:
            logger.warning(f"Error saving the activity log: {e}")

        phone_number = sender.split(":")[1]

        # ✅ Step 1: AI Processing (Extract activities)
        from langfuse import get_client

        langfuse = get_client()
        ai_client = get_ai_client()

        with langfuse.start_as_current_generation(
            name="AI Activity Extraction",
            model="gemini-2.5-flash-lite-preview-06-17",
            input={"message": message},
            metadata={"component": "activity_handler", "step": "ai_extraction"},
        ) as ai_span:
            assistant_start_time = time.perf_counter()

            activity_instructions = _load_activity_instructions()

            # Add current date context to instructions
            now = datetime.now()
            current_date = now.strftime("%Y-%m-%d")
            current_day = now.strftime("%A")  # e.g., "Monday"

            enhanced_instructions = f"""CURRENT DATE CONTEXT:
                                        Today is {current_date} ({current_day})

                                        Use this date to calculate relative dates:
                                        - "tomorrow" = {(now + timedelta(days=1)).strftime("%Y-%m-%d")}
                                        - "next week" = {(now + timedelta(days=7)).strftime("%Y-%m-%d")}
                                        - "in 2 weeks" = {(now + timedelta(days=14)).strftime("%Y-%m-%d")}
                                        - "in 3 days" = {(now + timedelta(days=3)).strftime("%Y-%m-%d")}
                                        - "next Monday" = {(now + timedelta(days=(7 - now.weekday()))).strftime("%Y-%m-%d")}
                                        - "next Friday" = {(now + timedelta(days=(4 - now.weekday() + 7) if now.weekday() >= 4 else (4 - now.weekday()))).strftime("%Y-%m-%d")}

                                        IMPORTANT: Always convert relative dates to YYYY-MM-DD format using the calculations above.

                                        {activity_instructions}"""

            # Make AI API call
            ai_response = await ai_client.chat_completion(
                messages=[
                    {"role": "system", "content": enhanced_instructions},
                    {"role": "user", "content": message},
                ],
                model="gemini-2.5-flash-lite-preview-06-17",
                temperature=0.01,
                max_tokens=10000,
                response_format={"type": "json_object"},  # Ensures JSON output
            )

            assistant_duration = time.perf_counter() - assistant_start_time
            logger.info(
                f"🕒 AI processing took {assistant_duration:.4f} sec using {ai_response.provider.value}"
            )

            # Parse the assistant response
            try:
                assistant_response = json.loads(ai_response.content)
                ai_span.update(
                    output={
                        "activities_found": len(
                            assistant_response.get("activities", [])
                        ),
                        "processing_time": assistant_duration,
                    }
                )
            except (json.JSONDecodeError, AttributeError) as e:
                error_msg = f"⚠ Failed to parse AI response: {str(e)}"
                send_whatsapp_message(sender, error_msg)
                logger.error(f"JSON parse error: {e}")
                logger.error(f"Raw AI response: {ai_response.content}")
                ai_span.update(output={"error": str(e)})
                if trace_id:
                    await trace_error(trace_id, sender, error_msg, str(e))
                return

        # Trace assistant call
        if trace_id:
            await trace_assistant(
                trace_id,
                f"chat-completions-{ai_response.model}-activity",
                message,
                {
                    "response_received": assistant_response is not None,
                    "model": ai_response.model,
                    "provider": ai_response.provider.value,
                    "tokens_used": (
                        ai_response.usage["total_tokens"] if ai_response.usage else None
                    ),
                },
                assistant_duration,
            )

        if not assistant_response or not assistant_response.get("activities"):
            error_msg = "⚠ Could not identify any tasks or deals in your message. Please try again with more details."
            send_whatsapp_message(sender, error_msg)

            # Trace error response
            if trace_id:
                await trace_response(trace_id, sender, error_msg, False)
            return

        # ✅ Step 2: Player and Club Lookup
        with langfuse.start_as_current_span(
            name="Activity Entity Lookup",
            input={"activities": assistant_response.get("activities", [])},
            metadata={"component": "activity_handler", "step": "entity_lookup"},
        ) as lookup_span:
            lookup_start = time.perf_counter()

            # Process each activity for player/club lookups
            activities = assistant_response.get("activities", [])
            processed_activities = []

            for activity in activities:
                # Ensure assigned_to_record is always an array (backend requirement)
                if "assigned_to_record" not in activity:
                    activity["assigned_to_record"] = []

                # Ensure playerId is always initialized (backend requirement)
                if "playerId" not in activity:
                    activity["playerId"] = None

                # Ensure teamId is always initialized (backend requirement)
                if "teamId" not in activity:
                    activity["teamId"] = None

                # Process player lookup if needed
                player_name = activity.get("player")
                if player_name and player_name != "Unknown":
                    try:
                        # Check player exists using secure client
                        client = get_secure_http_client()
                        player_response = client.get(
                            "/player_records/whatsapp/check_player",
                            params={
                                "phone_number": phone_number,
                                "player_name": player_name,
                            },
                            sender=sender,
                            timeout=10,
                        )

                        if player_response.success:
                            player_data = player_response.data

                            # Use the main player data (exact match)
                            try:
                                activity["playerId"] = int(
                                    player_data.get("playerId", 0)
                                )
                                activity["player_name"] = player_data.get(
                                    "fullName", player_name
                                )
                                logger.info(
                                    f"Player lookup successful: '{player_name}' -> '{activity['player_name']}' (ID: {activity['playerId']}, similarity: {player_data.get('similarity_score', 0):.3f})"
                                )
                            except (ValueError, TypeError):
                                logger.warning(
                                    f"Invalid playerId format: {player_data.get('playerId')}"
                                )
                                activity["playerId"] = None
                                activity["player_name"] = player_name

                            # Store top_matches for potential player changes via buttons
                            activity["player_top_matches"] = player_data.get(
                                "top_matches", []
                            )

                        else:
                            # Non-blocking - continue with original player name
                            activity["playerId"] = None
                            logger.warning(
                                f"Player '{player_name}' not found in portfolio"
                            )
                    except Exception as e:
                        logger.warning(f"Error during player lookup: {e}")
                        activity["playerId"] = None

                # Process club lookup if needed
                club_name = activity.get("club")
                logger.info(f"🔍 Processing club lookup for: '{club_name}'")
                if club_name and club_name != "Unknown":
                    try:
                        # Lookup club using team API
                        lookup_url = f"{settings.LOOKUP_API_URL}/teams/updated_lookup/{club_name}"

                        resp = await get_async_response(
                            lookup_url,
                            auth=(settings.LOOKUP_API_USR, settings.LOOKUP_API_PASS),
                        )

                        if resp.status_code < 400:
                            try:
                                response_data = resp.json()

                                # Handle both list and dict response formats
                                if isinstance(response_data, list):
                                    teams = response_data
                                elif isinstance(response_data, dict):
                                    teams = response_data.get("teams", [])
                                else:
                                    teams = []

                                logger.info(
                                    f"🎯 Found {len(teams)} teams for '{club_name}'"
                                )

                                if teams:
                                    # Use the team selection utility for consistent logic
                                    (
                                        best_match,
                                        skip_confirmation,
                                        sorted_alternatives,
                                    ) = select_best_team_match(teams, club_name)

                                    if best_match:
                                        # Convert teamId to integer for API
                                        team_id = best_match.get("teamId")
                                        if team_id is not None:
                                            try:
                                                activity["teamId"] = int(
                                                    team_id
                                                )  # API expects integer
                                            except (ValueError, TypeError):
                                                logger.warning(
                                                    f"Invalid teamId format: {team_id}"
                                                )
                                                activity["teamId"] = None
                                        else:
                                            activity["teamId"] = None

                                        backend_club_name = best_match.get(
                                            "name", club_name
                                        )
                                        activity["club_name"] = backend_club_name
                                        activity["area_name"] = best_match.get(
                                            "area_name", "Unknown"
                                        )

                                        # Store top_matches for potential team changes via buttons
                                        # Use sorted alternatives from the selection utility
                                        activity["team_top_matches"] = [
                                            best_match
                                        ] + sorted_alternatives[
                                            :4
                                        ]  # Best match + top 4 alternatives

                                        logger.info(
                                            f"✅ Club lookup successful: '{club_name}' -> '{backend_club_name}' (ID: {activity['teamId']}, Area: {activity['area_name']})"
                                        )
                                    else:
                                        logger.warning(
                                            f"⚠️ No suitable team match found for '{club_name}'"
                                        )
                                        activity["teamId"] = None
                                else:
                                    logger.warning(
                                        f"⚠️ Teams array is empty for '{club_name}'"
                                    )
                                    activity["teamId"] = None
                            except Exception as json_error:
                                logger.warning(
                                    f"❌ Failed to parse response JSON: {json_error}"
                                )
                                activity["teamId"] = None
                        else:
                            # Non-blocking - continue with original club name
                            activity["teamId"] = None
                            logger.warning(
                                f"❌ Club lookup API error for '{club_name}': Status {resp.status_code}"
                            )
                    except Exception as e:
                        logger.warning(
                            f"💥 Exception during club lookup for '{club_name}': {e}"
                        )
                        activity["teamId"] = None
                else:
                    logger.info(f"⏭️ Skipping club lookup - club_name: '{club_name}'")

                # Process contact lookup for assigned_to if needed
                assigned_to = activity.get("assigned_to")
                if assigned_to:
                    # Use the contact lookup utility for consistent logic
                    contact_id, contact_data = await lookup_contact(
                        assigned_to, phone_number, sender
                    )

                    # Format contact data for activity using utility function
                    contact_info = format_contact_for_activity(contact_data)
                    activity["assigned_to_record"] = contact_info["assigned_to_record"]
                    activity["assigned_to"] = contact_info["assigned_to"]
                else:
                    # No contact specified - use defaults
                    activity["assigned_to_record"] = []
                    activity["assigned_to"] = "you"

                # Update title with actual player name if lookup was successful
                update_activity_title_with_lookup_data(activity)

                processed_activities.append(activity)

            lookup_duration = time.perf_counter() - lookup_start
            logger.info(f"🔍 Entity lookup completed in {lookup_duration:.4f} sec")

            lookup_span.update(
                output={
                    "activities_processed": len(processed_activities),
                    "lookup_time": lookup_duration,
                }
            )

        # ✅ Step 3: Update User Session
        user_sessions[sender] = {
            "type": "activity",
            "status": "awaiting_confirmation",
            "activities": processed_activities,
            "current_activity_index": 0,
            "original_message": message,
        }

        # ✅ Step 4: Process First Activity
        await process_next_activity(sender, user_sessions)

    except Exception as e:
        error_msg = f"⚠ An error occurred while processing your activity: {str(e)}"
        send_whatsapp_message(sender, error_msg)
        logger.error(f"Error in process_activity_request: {e}", exc_info=True)
        if trace_id:
            await trace_error(trace_id, sender, error_msg, str(e))


async def process_next_activity(sender: str, user_sessions: dict):
    """Process the next activity in the queue and send confirmation message"""
    if sender not in user_sessions or user_sessions[sender].get("type") != "activity":
        logger.warning(f"No activity session found for {sender}")
        return

    session = user_sessions[sender]
    activities = session.get("activities", [])
    current_index = session.get("current_activity_index", 0)

    if not activities or current_index >= len(activities):
        # All activities processed
        send_whatsapp_message(sender, "✅ All activities have been processed!")
        user_sessions.pop(sender, None)  # Clear session
        return

    # Get current activity
    current_activity = activities[current_index]
    activity_type = current_activity.get("type", "unknown")

    # Format confirmation message based on activity type
    if activity_type == "task":
        confirmation_msg = format_task_confirmation(current_activity)
    elif activity_type == "deal":
        confirmation_msg = format_deal_confirmation(current_activity)
    else:
        confirmation_msg = "⚠ Unknown activity type. Please try again."
        user_sessions.pop(sender, None)  # Clear session
        send_whatsapp_message(sender, confirmation_msg)
        return

    # If multiple activities, show count
    total_activities = len(activities)
    if total_activities > 1:
        activity_counts = count_activity_types(activities)
        confirmation_msg = (
            f"({current_index + 1}/{total_activities}) {confirmation_msg}"
        )

    # Send confirmation message with buttons
    send_whatsapp_message(sender, confirmation_msg)

    # Add small delay before sending template (like other handlers)
    time.sleep(0.5)

    # Use the new 3-button activity template
    template_id = "HXddcc9f9dae477754421c862db6e99719"
    logger.info(f"Sending activity template {template_id} to {sender}")
    send_button_whatsapp_message(sender, template_id)

    # Update session status
    user_sessions[sender]["status"] = "awaiting_confirmation"


def format_task_confirmation(task: Dict[str, Any]) -> str:
    """Format task confirmation message"""
    title = task.get("title", "Untitled Task")
    due_date = task.get("due_date", "No deadline")
    assigned_to = task.get("assigned_to", "you")
    if assigned_to == "sender_default":
        assigned_to = "you"

    # Use the actual names from backend lookup if available
    player = task.get("player_name", task.get("player", ""))
    club = task.get("club_name", task.get("club", ""))
    staff = task.get("staff", "")

    # Format club name with country if available (using utility function)
    club_display = club
    if club and task.get("area_name"):
        club_display = format_team_display_name(
            {"name": club, "area_name": task.get("area_name")}
        )

    # Format message with bold formatting and rectangle before each data row
    message = f"🔹 *Type:* Task\n"
    message += f"🔹 *Title:* {title}\n"
    message += f"🔹 *Due:* {due_date}\n"
    message += f"🔹 *Assigned to:* {assigned_to}\n"

    if player:
        message += f"🔹 *Player:* {player}\n"
    if club:
        message += f"🔹 *Club:* {club_display}\n"
    if staff:
        message += f"🔹 *Staff:* {staff}\n"

    return message


def format_deal_confirmation(deal: Dict[str, Any]) -> str:
    """Format deal confirmation message"""
    title = deal.get("title", "Untitled Deal")
    stage = deal.get("stage", "to_do").replace("_", " ").title()
    next_action = deal.get("next_action", "No next action")
    assigned_to = deal.get("assigned_to", "you")
    if assigned_to == "sender_default":
        assigned_to = "you"

    # Use the actual names from backend lookup if available
    player = deal.get("player_name", deal.get("player", ""))
    club = deal.get("club_name", deal.get("club", ""))
    fee = deal.get("transfer_fee", "")
    staff = deal.get("staff", "")

    # Format club name with country if available (using utility function)
    club_display = club
    if club and deal.get("area_name"):
        club_display = format_team_display_name(
            {"name": club, "area_name": deal.get("area_name")}
        )

    # Format message with bold formatting and rectangle before each data row
    message = f"🔹 *Type:* Deal\n"
    message += f"🔹 *Title:* {title}\n"
    message += f"🔹 *Stage:* {stage}\n"
    message += f"🔹 *Next Action:* {next_action}\n"
    message += f"🔹 *Assigned to:* {assigned_to}\n"

    if player:
        message += f"🔹 *Player:* {player}\n"
    if club:
        message += f"🔹 *Club:* {club_display}\n"
    if fee:
        if isinstance(fee, (int, float)) and fee >= 1000:
            if fee >= 1000000:
                message += f"🔹 *Fee:* €{fee/1000000:.1f}M\n"
            else:
                message += f"🔹 *Fee:* €{fee/1000:.0f}k\n"
        else:
            message += f"🔹 *Fee:* {fee}\n"
    if staff:
        message += f"🔹 *Staff:* {staff}\n"

    return message


def update_activity_title_with_lookup_data(activity: Dict[str, Any]) -> None:
    """Update activity title with actual player and club names from lookup if available"""
    title = activity.get("title", "")
    player_name = activity.get("player_name")  # From lookup
    club_name = activity.get("club_name")  # From lookup

    # Only update if we have a title with "→" format
    if title and "→" in title:
        parts = title.split("→", 1)  # Split only on first occurrence
        if len(parts) == 2:
            left_part = parts[0].strip()
            right_part = parts[1].strip()

            # Replace player name (left side) if we have a successful lookup
            if player_name:
                left_part = player_name.strip()

            # Replace club name (right side) if we have a successful lookup
            # Handle different formats: "Club", "Unknown (Country)", "Top 5 (Region)"
            if club_name:
                # If the right part contains parentheses, preserve the format but replace the club name
                if "(" in right_part and ")" in right_part:
                    # Extract the parenthetical part
                    paren_start = right_part.find("(")
                    paren_part = right_part[paren_start:]  # "(Country)" or "(Region)"
                    right_part = f"{club_name.strip()} {paren_part}"
                else:
                    # Simple club name replacement
                    right_part = club_name.strip()

            # Reconstruct the title
            updated_title = f"{left_part} → {right_part}"

            # Only update if something actually changed
            if updated_title != title:
                activity["title"] = updated_title
                logger.info(f"Updated activity title: '{title}' → '{updated_title}'")


def count_activity_types(activities: List[Dict[str, Any]]) -> Dict[str, int]:
    """Count the number of each activity type"""
    counts = {}
    for activity in activities:
        activity_type = activity.get("type", "unknown")
        counts[activity_type] = counts.get(activity_type, 0) + 1
    return counts


async def handle_activity_confirmation(sender: str, message: str, user_sessions: dict):
    """Handle confirmation responses for activity creation"""
    session = user_sessions.get(sender, {})
    if (
        session.get("type") != "activity"
        or session.get("status") != "awaiting_confirmation"
    ):
        send_whatsapp_message(sender, "⚠ No pending activity found.")
        return

    activities = session.get("activities", [])
    current_index = session.get("current_activity_index", 0)

    if current_index >= len(activities):
        send_whatsapp_message(sender, "⚠ No more activities to process.")
        user_sessions.pop(sender, None)  # Clear session
        return

    current_activity = activities[current_index]

    # Handle template button responses
    if message == "proceed_create_activity":
        # Confirm and create activity
        await create_activity(sender, current_activity, user_sessions)

        # Move to next activity
        user_sessions[sender]["current_activity_index"] = current_index + 1
        await process_next_activity(sender, user_sessions)

    elif message == "change_player_activity":
        # Show player selection for change
        await show_player_selection_for_activity(sender, session, user_sessions)

    elif message == "change_team_activity":
        # Show team selection for change
        await show_team_selection_for_activity(sender, session, user_sessions)

    else:
        send_whatsapp_message(
            sender,
            "⚠ Invalid selection. Please use the buttons to proceed or make changes.",
        )


async def create_activity(sender: str, activity: Dict[str, Any], user_sessions: dict):
    """Create activity via API call"""
    try:
        phone_number = sender.split(":")[1]
        activity_type = activity.get("type", "unknown")

        # Validate activity type
        if activity_type not in ["task", "deal"]:
            send_whatsapp_message(sender, f"⚠ Unknown activity type: {activity_type}")
            return

        # Prepare bulk request payload - API expects list of activities
        bulk_payload = [activity]

        # Make secure API call
        client = get_secure_http_client()
        api_response = client.post(
            "/activity/whatsapp",
            json_data=bulk_payload,  # Send as list
            params={"phone_number": phone_number},
            sender=sender,
        )

        # Handle response
        if api_response.success:
            activity_type_display = "task" if activity_type == "task" else "deal"
            tokens_left = api_response.data.get("tokens_left", "N/A")

            # Don't send success message for each activity in a multi-activity flow
            # The process_next_activity function will handle messaging
            if len(user_sessions[sender].get("activities", [])) <= 1:
                send_whatsapp_message(
                    sender,
                    f"✅ {activity_type_display.title()} created successfully! Tokens left: {tokens_left}",
                )
        else:
            # Only send error for non-auth/rate limit errors (those are handled by client)
            if api_response.status_code not in [401, 403, 429]:
                send_whatsapp_message(
                    sender, f"⚠ Failed to create {activity_type}. Please try again."
                )

    except Exception as e:
        send_whatsapp_message(sender, f"⚠ Error creating activity: {str(e)}")
        logger.error(f"Error in create_activity: {e}", exc_info=True)


async def show_player_selection_for_activity(
    sender: str, session: Dict[str, Any], user_sessions: dict
):
    """Show player selection options for activity change using top_matches from initial lookup"""
    activities = session.get("activities", [])
    current_index = session.get("current_activity_index", 0)

    if current_index >= len(activities):
        send_whatsapp_message(sender, "⚠ Activity not found.")
        return

    current_activity = activities[current_index]
    top_matches = current_activity.get("player_top_matches", [])

    if not top_matches:
        send_whatsapp_message(sender, "⚠ No alternative player matches found.")
        # Go back to confirmation
        user_sessions[sender]["status"] = "awaiting_confirmation"
        await process_next_activity(sender, user_sessions)
        return

    # Update session status
    user_sessions[sender]["status"] = "awaiting_player_selection"

    # Build selection message
    player_list = "*SELECT PLAYER*\n\n"
    for i, player in enumerate(top_matches[:5], 1):  # Limit to 5 like player update
        player_name = player.get("fullName", "Unknown")
        player_list += f"🔹 {i}. {player_name}\n"

    player_list += "\nPlease send the number of your choice (1-5):"

    send_whatsapp_message(sender, player_list)


async def show_team_selection_for_activity(
    sender: str, session: Dict[str, Any], user_sessions: dict
):
    """Show team selection options for activity change using top_matches from initial lookup"""
    activities = session.get("activities", [])
    current_index = session.get("current_activity_index", 0)

    if current_index >= len(activities):
        send_whatsapp_message(sender, "⚠ Activity not found.")
        return

    current_activity = activities[current_index]
    top_matches = current_activity.get("team_top_matches", [])

    if not top_matches:
        send_whatsapp_message(sender, "⚠ No alternative team matches found.")
        # Go back to confirmation
        user_sessions[sender]["status"] = "awaiting_confirmation"
        await process_next_activity(sender, user_sessions)
        return

    # Update session status
    user_sessions[sender]["status"] = "awaiting_team_selection"

    # Build selection message (similar to team handler format)
    team_list = "*SELECT TEAM*\n\n"
    for i, team in enumerate(top_matches[:5], 1):  # Limit to 5 like player update
        team_name = team.get("name", "Unknown")
        area_name = team.get("area_name", "Unknown")
        division_level = team.get("division_level", "N/A")
        team_list += f"🔹 {i}. {team_name} ({area_name}, {division_level} division)\n"

    team_list += "\nPlease send the number of your choice (1-5):"

    send_whatsapp_message(sender, team_list)


async def handle_activity_player_selection(
    sender: str, message: str, user_sessions: dict
):
    """Handle player selection for activity change"""
    session = user_sessions.get(sender, {})
    if (
        session.get("type") != "activity"
        or session.get("status") != "awaiting_player_selection"
    ):
        send_whatsapp_message(sender, "⚠ No pending player selection found.")
        return

    try:
        choice = int(message.strip())
        activities = session.get("activities", [])
        current_index = session.get("current_activity_index", 0)

        if current_index >= len(activities):
            send_whatsapp_message(sender, "⚠ Activity not found.")
            return

        current_activity = activities[current_index]
        top_matches = current_activity.get("player_top_matches", [])

        if 1 <= choice <= len(top_matches) and choice <= 5:
            # Update selected player
            new_player = top_matches[choice - 1]

            # Update current activity with new player data
            activities[current_index]["player"] = new_player.get("fullName")
            activities[current_index]["player_name"] = new_player.get("fullName")

            # Convert playerId to integer for API
            player_id = new_player.get("playerId")
            if player_id is not None:
                try:
                    activities[current_index]["playerId"] = int(player_id)
                except (ValueError, TypeError):
                    logger.warning(f"Invalid playerId format: {player_id}")
                    activities[current_index]["playerId"] = None
            else:
                activities[current_index]["playerId"] = None

            # Keep the top_matches for potential future changes
            activities[current_index]["player_top_matches"] = top_matches

            # Update title with the new player name
            update_activity_title_with_lookup_data(activities[current_index])

            send_whatsapp_message(
                sender, f"✅ Player updated to: {new_player.get('fullName')}"
            )

            # Reset status and show confirmation again
            user_sessions[sender]["status"] = "awaiting_confirmation"
            await process_next_activity(sender, user_sessions)
        else:
            send_whatsapp_message(
                sender,
                f"⚠ Invalid selection. Please choose a number between 1 and {min(len(top_matches), 5)}.",
            )

    except ValueError:
        send_whatsapp_message(sender, "⚠ Invalid input. Please enter a number.")


async def handle_activity_team_selection(
    sender: str, message: str, user_sessions: dict
):
    """Handle team selection for activity change"""
    session = user_sessions.get(sender, {})
    if (
        session.get("type") != "activity"
        or session.get("status") != "awaiting_team_selection"
    ):
        send_whatsapp_message(sender, "⚠ No pending team selection found.")
        return

    try:
        choice = int(message.strip())
        activities = session.get("activities", [])
        current_index = session.get("current_activity_index", 0)

        if current_index >= len(activities):
            send_whatsapp_message(sender, "⚠ Activity not found.")
            return

        current_activity = activities[current_index]
        top_matches = current_activity.get("team_top_matches", [])

        if 1 <= choice <= len(top_matches) and choice <= 5:
            # Update selected team
            new_team = top_matches[choice - 1]

            # Update current activity with new team data
            activities[current_index]["club"] = new_team.get("name")
            activities[current_index]["club_name"] = new_team.get("name")

            # Convert teamId to integer for API
            team_id = new_team.get("teamId")
            if team_id is not None:
                try:
                    activities[current_index]["teamId"] = int(team_id)
                except (ValueError, TypeError):
                    logger.warning(f"Invalid teamId format: {team_id}")
                    activities[current_index]["teamId"] = None
            else:
                activities[current_index]["teamId"] = None

            activities[current_index]["area_name"] = new_team.get(
                "area_name", "Unknown"
            )

            # Keep the top_matches for potential future changes
            activities[current_index]["team_top_matches"] = top_matches

            # Update title with the new team name
            update_activity_title_with_lookup_data(activities[current_index])

            send_whatsapp_message(
                sender,
                f"✅ Team updated to: {new_team.get('name')} ({new_team.get('area_name', 'Unknown')})",
            )

            # Reset status and show confirmation again
            user_sessions[sender]["status"] = "awaiting_confirmation"
            await process_next_activity(sender, user_sessions)
        else:
            send_whatsapp_message(
                sender,
                f"⚠ Invalid selection. Please choose a number between 1 and {min(len(top_matches), 5)}.",
            )

    except ValueError:
        send_whatsapp_message(sender, "⚠ Invalid input. Please enter a number.")
