from sqlalchemy import create_engine, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from app.config import settings
from sqlalchemy.orm import Session
import logging

logger = logging.getLogger(__name__)

# Create the SQLAlchemy engine with enhanced error handling for Cloud Run
try:
    engine = create_engine(
        settings.PG_URL_PROD,
        pool_size=1,  # Max number of connections to keep open
        max_overflow=5,  # Allow 5 extra temporary connections if needed
        pool_timeout=30,  # How long to wait for a connection before failing
        pool_recycle=1800,  # Reuse connections every 30 minutes to avoid idle timeouts
        pool_pre_ping=True,  # Automatically checks if connection is alive before using it
        connect_args={"connect_timeout": 10},  # Add connection timeout for Cloud SQL
    )
    logger.info("Database engine created successfully")
except Exception as e:
    logger.error(f"Failed to create database engine: {e}")
    # Create a dummy engine that will fail gracefully
    engine = None

# Create a SessionLocal class (only if engine is available)
if engine:
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
else:
    SessionLocal = None

# Base class for models
Base = declarative_base()


# Dependency to get DB session
def get_db():
    if not SessionLocal:
        raise RuntimeError("Database not available")
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


# Use a session to execute the query
def insert_whatsapp_request(phone_number: str, message: str):
    if not engine:
        logger.warning("Database not available, skipping WhatsApp request insertion")
        return

    try:
        with Session(engine) as session:
            session.execute(
                text(
                    "INSERT INTO whatsapp_requests (phone_number, message, created_at) VALUES (:phone_number, :message, now())"
                ),
                {"phone_number": phone_number, "message": message},
            )
            session.commit()  # Commit the transaction
    except Exception as e:
        logger.error(f"Failed to insert WhatsApp request: {e}")
        # Don't raise the exception - let the app continue without database logging
