"""
Team selection utility functions for consistent team filtering and sorting logic.
Used by both team handler and activity handler.
"""

import logging
from typing import List, Dict, Any, Tuple, Optional

logger = logging.getLogger(__name__)


def select_best_team_match(
    teams: List[Dict[str, Any]], 
    team_name: str
) -> Tuple[Optional[Dict[str, Any]], bool, List[Dict[str, Any]]]:
    """
    Select the best team match from a list of teams using consistent filtering and sorting logic.
    
    Args:
        teams: List of team dictionaries from the lookup API
        team_name: Original team name being searched for (for logging)
        
    Returns:
        Tuple of (best_match, skip_confirmation, sorted_alternatives)
        - best_match: The selected best team match or None if no suitable match
        - skip_confirmation: True if this is an exact match that should skip user confirmation
        - sorted_alternatives: List of remaining teams sorted by rating for user selection
    """
    if not teams:
        logger.warning(f"⚠️ No teams provided for selection")
        return None, False, []
    
    logger.info(f"🔍 Selecting best match from {len(teams)} teams for '{team_name}'")
    
    # Determine the best match based on similarity score ranges
    best_match = None
    skip_confirmation = False

    # Check for exact matches (0.0 - 0.1) with rating >= 1400
    exact_matches = [
        team
        for team in teams
        if team["similarity_score"] <= 0.1
        and team["smoothed_rating"] >= 1400
    ]
    
    if exact_matches:
        # For exact matches, pick the one with highest smoothed_rating
        best_match = max(
            exact_matches, key=lambda x: x["smoothed_rating"]
        )
        skip_confirmation = True
        logger.info(
            f"🎯 Exact match found for {team_name}: {best_match['name']} "
            f"(rating: {best_match['smoothed_rating']}, score: {best_match['similarity_score']:.3f})"
        )
    else:
        # For similarity > 0.1: Sort by rating, but check if top 2 teams have ratings within 300 points
        non_exact_matches = [
            team
            for team in teams
            if team["similarity_score"] > 0.1
            and team["similarity_score"] <= 0.75
        ]

        if non_exact_matches:
            # Sort by rating (highest first)
            rating_sorted_teams = sorted(
                non_exact_matches,
                key=lambda x: x["smoothed_rating"],
                reverse=True,
            )

            # Check if we have at least 2 teams and their ratings are within 300 points
            if (
                len(rating_sorted_teams) >= 2
                and rating_sorted_teams[0]["smoothed_rating"]
                - rating_sorted_teams[1]["smoothed_rating"]
                <= 300
            ):
                # Pick the one with higher similarity (which is actually the lower) score between the top 2
                top_two = rating_sorted_teams[:2]
                best_match = min(
                    top_two, key=lambda x: x["similarity_score"]
                )

                logger.info(
                    f"🔄 Close ratings found for {team_name}: Selected {best_match['name']} "
                    f"(rating: {best_match['smoothed_rating']}, similarity: {best_match['similarity_score']:.3f}) "
                    f"over alternative with rating: {rating_sorted_teams[1 if best_match == rating_sorted_teams[0] else 0]['smoothed_rating']}"
                )
            else:
                # Use the highest rated team
                best_match = rating_sorted_teams[0]
                logger.info(
                    f"✅ Best match found for {team_name}: {best_match['name']} "
                    f"(rating: {best_match['smoothed_rating']}, similarity: {best_match['similarity_score']:.3f})"
                )

    # Sort remaining teams for alternatives
    if best_match:
        remaining_teams = [
            team for team in teams if team != best_match
        ]
    else:
        remaining_teams = teams
        
    sorted_alternatives = sorted(
        remaining_teams,
        key=lambda x: x["smoothed_rating"],
        reverse=True,
    )
    
    logger.info(
        f"📋 Team selection complete for '{team_name}': "
        f"Best match: {best_match['name'] if best_match else 'None'}, "
        f"Skip confirmation: {skip_confirmation}, "
        f"Alternatives: {len(sorted_alternatives)}"
    )
    
    return best_match, skip_confirmation, sorted_alternatives


def format_team_display_name(team: Dict[str, Any]) -> str:
    """
    Format team name with country information for display.
    
    Args:
        team: Team dictionary with 'name' and 'area_name' fields
        
    Returns:
        Formatted team name string
    """
    team_name = team.get("name", "Unknown")
    area_name = team.get("area_name")
    
    if area_name:
        return f"{team_name} ({area_name})"
    return team_name
