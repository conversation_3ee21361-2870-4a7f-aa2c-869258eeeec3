# WhatsApp Bot Commands Guide

## Overview
Our WhatsApp bot now supports **command-based routing** for maximum efficiency and accuracy. Users can use specific commands to guarantee their messages are routed to the correct handler, or rely on automatic detection.

## Command-Based Routing (Recommended)

### 🎯 Player Creation Commands
Use these commands when you want to create a new player record:

**Commands:**
- `Create player [Transfermarkt URL]`
- `Add player [Transfermarkt URL]`
- `New player [Transfermarkt URL]`

**Examples:**
```
Create player https://www.transfermarkt.com/john-doe/profil/spieler/12345
Add player https://www.transfermarkt.com/jane-smith/profil/spieler/67890
New player https://www.transfermarkt.com/alex-jones/profil/spieler/11111
```

### ✏️ Player Update Commands
Use these commands when you want to update existing player information:

**Commands:**
- `Update player [player name] [details]`
- `Modify player [player name] [details]`
- `Edit player [player name] [details]`

**Examples:**
```
Update player <PERSON> salary to 120k
Modify player <PERSON> position to CAM, assigned to <PERSON> player <PERSON> control stage to in talks
```

### 📋 Task Creation Commands
Use these commands when you want to create tasks or deals:

**Commands:**
- `Create task [task description]`
- `Add task [task description]`
- `New task [task description]`
- `Create deal [deal description]`
- `Add deal [deal description]`
- `New deal [deal description]`

**Examples:**
```
Create task: Call Markus about Smolenski tomorrow
Add task: Send video to Cracovia by Friday
New task: Follow up with Barcelona about contract
Create deal with Real Madrid for Marinov
Add deal: Negotiate with Botev Plovdiv about Smolenski
New deal: Propose Smolenski to Levski, CSKA and Botev
```

## Automatic Detection (Still Works)

### 🏈 Team Requests (No Commands Needed)
Team requests work automatically when you mention:
- Team name + position(s)
- Player requirements for specific teams

**Examples:**
```
Levski needs a CMF and a RW, budget 1.2m
Barcelona is looking for a striker, 25-28 years old
Real Madrid wants a goalkeeper with La Liga experience
```

## Benefits of Using Commands

1. **🎯 100% Accuracy**: Commands guarantee correct routing
2. **⚡ Faster Processing**: No ambiguity means quicker responses
3. **🔄 Consistent Results**: Same command always goes to same handler
4. **📚 Easy to Learn**: Simple, intuitive command structure

**Example Response:**
```
I couldn't understand your request. Please try rephrasing your message or contact support for assistance.

Suggestions:
- Try including a Transfermarkt URL or use 'Create player' command
- Use commands like 'Update player', 'Create task', or specify team name with position
```

## Quick Reference

| Action | Command Format | Example |
|--------|----------------|---------|
| Create Player | `Create player [URL]` | `Create player https://transfermarkt.com/...` |
| Update Player | `Update player [name] [details]` | `Update player John Smith salary 120k` |
| Create Task | `Create task [description]` | `Create task: Call agent tomorrow` |
| Create Deal | `Create deal [description]` | `Create deal with Barcelona €2M` |
| Team Request | No command needed | `Levski needs CMF and RW, budget 1.2m` |

## Tips for Best Results

1. **Use commands when possible** - they guarantee correct routing
2. **Be specific** - include player names, team names, and clear details
3. **Include context** - mention positions, budgets, deadlines when relevant
4. **One request per message** - avoid mixing different types of requests

---

*This command system ensures your messages are processed quickly and accurately by the right handler every time!*
