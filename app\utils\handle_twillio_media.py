import aiohttp
import os
from openai import OpenAI
from app.utils.whatsapp_handler import send_whatsapp_message
import pdfplumber
import pytesseract
import aiohttp
from PIL import Image
import io
from langfuse import observe
import google.generativeai as genai
from app.utils import get_absolute_path
import tempfile

# Initialize OpenAI client (for fallback)
openai_client = OpenAI(api_key=os.environ.get("OPENAI_API_KEY"))

# Initialize Gemini client
gemini_api_key = os.getenv("GEMINI_API_KEY")
if gemini_api_key:
    genai.configure(api_key=gemini_api_key)
    gemini_client = genai.GenerativeModel("gemini-2.5-flash")
else:
    gemini_client = None


async def download_twilio_media(media_url: str) -> bytes:
    """
    Downloads media (audio, documents, images, etc.) from Twilio using authentication.
    """
    try:
        # Twilio credentials
        TWILIO_ACCOUNT_SID = os.getenv("TWILIO_ACCOUNT_SID")
        TWILIO_AUTH_TOKEN = os.getenv("TWILIO_AUTH_TOKEN")

        auth = aiohttp.BasicAuth(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN)

        async with aiohttp.ClientSession() as session:
            async with session.get(media_url, auth=auth) as response:
                print(f"Twilio Media Response: {response.status}")

                if response.status == 200:
                    return await response.read()  # Return file bytes
                else:
                    print(f"Failed to download media. Status: {response.status}")
                    return None

    except Exception as e:
        print(f"Error downloading media: {e}")
        return None


@observe(name="Gemini Image Processing - Inline")
async def extract_text_from_image(file_data: bytes) -> str:
    """
    Extracts text from an image using Gemini with OCR + context understanding.
    Falls back to Tesseract OCR if Gemini fails.
    """
    try:
        if not gemini_client:
            raise Exception("Gemini client not available, falling back to Tesseract")

        # Check file size (Gemini inline limit: 20MB, WhatsApp images: 5MB max)
        file_size_mb = len(file_data) / (1024 * 1024)
        if file_size_mb > 20:
            raise Exception(
                f"Image file too large ({file_size_mb:.1f}MB) for Gemini inline processing (max 20MB)"
            )

        # Create inline image part
        import base64

        image_data = base64.b64encode(file_data).decode("utf-8")

        # Generate text extraction using Gemini with inline image
        response = gemini_client.generate_content(
            [
                "Extract all text from this image. If there's no text, describe what you see in the image. Be concise and accurate.",
                {
                    "inline_data": {
                        "mime_type": "image/jpeg",  # Most common format, Gemini auto-detects
                        "data": image_data,
                    }
                },
            ]
        )

        if response.text:
            return response.text.strip()
        else:
            raise Exception("Gemini returned empty response")

    except Exception as gemini_error:
        print(
            f"Gemini image processing failed: {gemini_error}, falling back to Tesseract OCR"
        )

        # Fallback to Tesseract OCR
        try:
            image = Image.open(io.BytesIO(file_data))
            extracted_text = pytesseract.image_to_string(image)

            return (
                extracted_text.strip()
                if extracted_text
                else "⚠ No readable text found in the image."
            )
        except Exception as tesseract_error:
            print(f"Tesseract OCR also failed: {tesseract_error}")
            return None


@observe(name="Gemini Audio Transcription - Inline")
async def transcribe_audio_with_gemini(file_data: bytes) -> str:
    """
    Transcribes audio using Gemini 2.5 Flash with inline audio data.
    Falls back to OpenAI Whisper if Gemini fails.
    Optimized for speed by avoiding file I/O operations.
    """
    try:
        if not gemini_client:
            raise Exception("Gemini client not available, falling back to OpenAI")

        # Check file size (Gemini inline limit: 20MB, WhatsApp audio: 16MB max)
        file_size_mb = len(file_data) / (1024 * 1024)
        if file_size_mb > 20:
            raise Exception(
                f"Audio file too large ({file_size_mb:.1f}MB) for Gemini inline processing (max 20MB)"
            )

        # Create inline audio part - no temp files needed!
        import base64

        audio_data = base64.b64encode(file_data).decode("utf-8")

        # Generate transcription using Gemini with inline audio
        response = gemini_client.generate_content(
            [
                "Please provide a transcript of this audio. Only return the transcribed text, no additional commentary.",
                {
                    "inline_data": {
                        "mime_type": "audio/mpeg",  # WhatsApp typically sends as MP3/MPEG
                        "data": audio_data,
                    }
                },
            ]
        )

        if response.text:
            return response.text.strip()
        else:
            raise Exception("Gemini returned empty response")

    except Exception as gemini_error:
        print(
            f"Gemini inline transcription failed: {gemini_error}, falling back to OpenAI Whisper"
        )

        # Fallback to OpenAI Whisper
        try:
            transcribed_text = openai_client.audio.transcriptions.create(
                model="whisper-1",
                file=("voice_message.mp3", file_data, "audio/mpeg"),
                response_format="text",
            )
            return transcribed_text
        except Exception as openai_error:
            print(f"OpenAI Whisper transcription also failed: {openai_error}")
            return None


@observe(name="Gemini PDF Processing - Inline")
async def extract_text_from_pdf(file_data: bytes) -> str:
    """
    Extracts text from a PDF using Gemini with document understanding.
    Falls back to pdfplumber if Gemini fails.
    """
    try:
        if not gemini_client:
            raise Exception("Gemini client not available, falling back to pdfplumber")

        # Check file size (Gemini inline limit: 20MB, WhatsApp documents: 16MB max)
        file_size_mb = len(file_data) / (1024 * 1024)
        if file_size_mb > 20:
            raise Exception(
                f"PDF file too large ({file_size_mb:.1f}MB) for Gemini inline processing (max 20MB)"
            )

        # Create inline PDF part
        import base64

        pdf_data = base64.b64encode(file_data).decode("utf-8")

        # Generate text extraction using Gemini with inline PDF
        response = gemini_client.generate_content(
            [
                "Extract all text content from this PDF document. Maintain the structure and formatting as much as possible. If there are images with text, extract that text too.",
                {
                    "inline_data": {
                        "mime_type": "application/pdf",
                        "data": pdf_data,
                    }
                },
            ]
        )

        if response.text:
            return response.text.strip()
        else:
            raise Exception("Gemini returned empty response")

    except Exception as gemini_error:
        print(
            f"Gemini PDF processing failed: {gemini_error}, falling back to pdfplumber"
        )

        # Fallback to pdfplumber
        try:
            # Create a temporary file with proper cleanup
            with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as temp_file:
                temp_file.write(file_data)
                temp_pdf_path = temp_file.name

            with pdfplumber.open(temp_pdf_path) as pdf:
                text = "\n".join(
                    [page.extract_text() for page in pdf.pages if page.extract_text()]
                )

            # Clean up temp file
            os.unlink(temp_pdf_path)

            return text.strip() if text else "⚠ No extractable text found in the PDF."

        except Exception as pdfplumber_error:
            print(f"pdfplumber also failed: {pdfplumber_error}")
            # Clean up temp file if it exists
            if "temp_pdf_path" in locals() and os.path.exists(temp_pdf_path):
                os.unlink(temp_pdf_path)
            return None


@observe(name="Media Processing")
async def handle_whatsapp_media(sender: str, media_url: str, media_type: str):
    """
    Handles different types of media (audio, PDF, images) from WhatsApp.
    """

    # Step 1: Download the file
    file_data = await download_twilio_media(media_url)
    if not file_data:
        send_whatsapp_message(
            sender, "⚠ Failed to download the file. Please try again."
        )
        return

    # Step 2: Handle Audio (Transcription) - Use Gemini with OpenAI fallback
    if "audio" in media_type:
        transcribed_text = await transcribe_audio_with_gemini(file_data)
        if transcribed_text:
            return transcribed_text
        else:
            send_whatsapp_message(sender, "⚠ Failed to process voice message.")
            return None

    # Step 3: Handle PDF (Extract text using OpenAI)
    extracted_text = None
    if "pdf" in media_type:
        extracted_text = await extract_text_from_pdf(file_data)

    # Step 4: Handle Images (Optionally, extract text with OCR)
    if "image" in media_type:
        extracted_text = await extract_text_from_image(file_data)

    if extracted_text:
        return extracted_text
    else:
        send_whatsapp_message(sender, "⚠ Failed to extract text from the document.")
        return None
