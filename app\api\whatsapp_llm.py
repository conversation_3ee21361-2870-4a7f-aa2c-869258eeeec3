from fastapi import APIRouter, Request, Depends
from fastapi.responses import PlainTextResponse
import os
import logging
import time
from app.utils.ai_client import get_ai_client
from langfuse import observe
from app.utils.whatsapp_handler import send_whatsapp_message
from app.utils.validate_phone import validate_whatsapp_user
from app.utils import ordinal_superscript
from sqlalchemy.orm import Session
from app.db import get_db, SessionLocal
from app.utils.create_requests_from_whatsapp import create_request_from_whatsapp
from app.utils.handle_twillio_media import handle_whatsapp_media
from app.teams.process_next_team import process_next_team
from app.players.handle_player_update_request import (
    handle_player_update_confirmation,
    handle_player_selection,
    handle_contact_selection,
)
from app.activities.handle_activity_request import (
    handle_activity_confirmation,
    handle_activity_player_selection,
    handle_activity_team_selection,
)

# Removed direct imports - now handled through router system
# from app.teams.handle_team_request import handle_team_request
# from app.players.handle_player_request import process_player_request
# from app.utils.is_player_request import is_player_request
from app.utils.message_buffer import add_message_to_buffer
from app.router import RouterAgent, HandlerRegistry

# Note: Old tracing imports removed - now using @observe decorator pattern

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# AI Client (Gemini with OpenAI fallback)
ai_client = get_ai_client()

# Initialize Router Agent and Handler Registry
router_agent = RouterAgent()
handler_registry = HandlerRegistry()

# FastAPI Router
router = APIRouter()

# Store user interaction state
user_sessions = {}


def get_db_session():
    """Get database session safely, return None if database is not available."""
    try:
        if SessionLocal:
            return SessionLocal()
        return None
    except Exception as e:
        logger.warning(f"Failed to create database session: {e}")
        return None


@observe(name="Session State Check")
async def check_session_state(sender: str, user_sessions: dict):
    """Check and initialize user session state."""
    if sender not in user_sessions:
        user_sessions[sender] = {}

    session = user_sessions.get(sender, {})

    return {
        "has_session": bool(session),
        "status": session.get("status"),
        "awaiting_confirmation": session.get("status") == "awaiting_confirmation",
        "awaiting_team_selection": session.get("awaiting_team_selection", False),
        "stored_requests_count": len(session.get("stored_requests", [])),
        "session_type": session.get("type"),
    }


@observe(name="Bot Action")
@router.post("/whatsapp-webhook", response_class=PlainTextResponse)
async def whatsapp_webhook(request: Request):
    """
    Handles incoming WhatsApp messages via Twilio.
    Sends user messages to OpenAI Assistant and returns the response.
    """
    # ⏱️ Start timing the entire webhook process
    webhook_start_time = time.perf_counter()

    form_data = await request.form()
    incoming_msg = form_data.get("Body", "").strip()
    sender = form_data.get("From", "").strip()
    button_payload = form_data.get("ButtonPayload", "").strip()

    # Set up root "Bot Action" trace
    from langfuse import get_client

    langfuse = get_client()
    langfuse.update_current_trace(
        user_id=sender,
        session_id=f"whatsapp_{sender}_{int(time.time())}",
        tags=["whatsapp", "bot_action"],
        input={"message": incoming_msg, "sender": sender, "form_data": dict(form_data)},
        metadata={
            "component": "whatsapp_webhook",
            "message_length": len(incoming_msg),
            "media_count": form_data.get("NumMedia", 0),
        },
    )

    # ⏱️ Time form data parsing
    form_parse_time = time.perf_counter() - webhook_start_time
    logger.info(f"📝 Form data parsed in {form_parse_time:.4f} sec")

    try:
        logger.info(f"Incoming message from {sender}: {incoming_msg}")

        phone_number = sender.split(":")[1]

        # ⏱️ User validation (direct call, no separate logging)
        db = get_db_session()
        if db:
            try:
                await validate_whatsapp_user(request, phone_number, db)
            finally:
                db.close()
        else:
            logger.warning("Database not available, skipping user validation")

        # Handle media if present
        num_media = int(form_data.get("NumMedia", 0))
        if num_media > 0:
            media_type = form_data.get("MediaContentType0", "")
            media_url = form_data.get("MediaUrl0", "")

            logger.info(f"📎 Media detected from {sender}: {media_type} - {media_url}")

            if media_url:
                # ⏱️ Time media processing
                media_start = time.perf_counter()
                try:
                    processed_media = await handle_whatsapp_media(
                        sender, media_url, media_type
                    )
                    media_time = time.perf_counter() - media_start
                    logger.info(
                        f"📎 Media processing completed in {media_time:.4f} sec"
                    )

                    if processed_media:
                        incoming_msg = processed_media
                    else:
                        logger.warning(f"Failed to process media from {sender}")
                        return None
                except Exception as e:
                    logger.error(f"Error processing media: {e}")
                    return None

        # ⏱️ WhatsApp Message Processing with @observe decorator
        await process_message_with_observe(
            sender, incoming_msg, button_payload, webhook_start_time
        )

        # ⏱️ Total webhook processing time (up to buffering)
        total_webhook_time = time.perf_counter() - webhook_start_time
        logger.info(f"🏁 Webhook processing completed in {total_webhook_time:.4f} sec")

        # Update Bot Action trace output
        langfuse.update_current_trace(
            output={
                "webhook_completed": True,
                "total_webhook_time": total_webhook_time,
                "validation_passed": True,
                "message_buffered": True,
            }
        )

        return None

    except Exception as e:
        logger.error(f"Error in webhook processing: {e}")
        # Update Bot Action trace with error
        langfuse.update_current_trace(
            output={
                "webhook_completed": False,
                "error": str(e),
                "validation_passed": False,
            }
        )
        return None


@observe(name="WhatsApp Message Processing")
async def process_message_with_observe(
    sender: str, incoming_msg: str, button_payload: str = "", webhook_start_time=None
):
    """WhatsApp message processing with @observe decorator for proper nesting."""
    processing_start = time.perf_counter()

    # Use button payload if available (for template buttons), otherwise use incoming message
    message_to_process = button_payload if button_payload else incoming_msg

    # Call the actual message processing function
    await process_final_message(sender, message_to_process, None, webhook_start_time)

    processing_time = time.perf_counter() - processing_start
    logger.info(f"📱 Message processing completed in {processing_time:.4f} sec")

    return {"processing_completed": True, "execution_time": processing_time}


async def process_final_message(
    sender, merged_message, thread_id=None, webhook_start_time=None
):
    """
    Processes the final merged message after the buffer delay.
    """
    try:

        # ⏱️ Start timing the final message processing
        final_processing_start = time.perf_counter()

        logger.info(
            f"🔄 Processing final message from {sender}: {merged_message[:50]}..."
        )

        # ⏱️ Log time from webhook start to final processing
        if webhook_start_time:
            time_to_final_processing = final_processing_start - webhook_start_time
            logger.info(
                f"⏰ Time from webhook start to final processing: {time_to_final_processing:.4f} sec"
            )

        # Check session state
        session_info = await check_session_state(sender, user_sessions)

        phone_number = sender.split(":")[1]

        if sender in user_sessions and user_sessions[sender].get(
            "awaiting_team_selection"
        ):
            logger.info(f"Handling team selection flow for {sender}")
            if merged_message.isdigit():
                selected_index = int(merged_message) - 1
                current_team_name = user_sessions[sender]["current_team"]
                original_team_name = next(
                    (
                        orig_name
                        for orig_name, data in user_sessions[sender][
                            "team_name_updates"
                        ].items()
                        if data.get("new_team_name") == current_team_name
                    ),
                    current_team_name,
                )
                lookup_options = (
                    user_sessions[sender]["team_name_updates"]
                    .get(original_team_name, {})
                    .get("top_teams", [])
                )

                if 0 <= selected_index < len(lookup_options):
                    chosen_team = lookup_options[selected_index]
                    new_team_name = chosen_team["name"]
                    new_team_id = chosen_team["teamId"]

                    user_sessions[sender]["team_name_updates"][original_team_name][
                        "new_team_name"
                    ] = new_team_name
                    user_sessions[sender]["team_name_updates"][original_team_name][
                        "teamId"
                    ] = new_team_id
                    user_sessions[sender]["awaiting_team_selection"] = False

                    for entry in user_sessions[sender]["stored_requests"]:
                        if entry["team_name"] == current_team_name:
                            entry["team_name"] = new_team_name
                            entry["teamId"] = new_team_id

                    updated_team_request_payload = [
                        entry
                        for entry in user_sessions[sender]["stored_requests"]
                        if entry["team_name"] == new_team_name
                    ]
                    response = await create_request_from_whatsapp(
                        sender, phone_number, updated_team_request_payload
                    )
                    # Handle next team processing with auto-processing support
                    from app.teams.handle_team_request import handle_auto_processing

                    if sender in user_sessions and user_sessions[sender].get(
                        "pending_teams"
                    ):
                        result = process_next_team(sender, user_sessions)
                        if result == "awaiting_confirmation":
                            user_sessions[sender]["status"] = "awaiting_confirmation"
                        elif result == "auto_process":
                            await handle_auto_processing(sender, user_sessions, result)
                    return None
                else:
                    logger.warning(
                        f"Invalid team selection from {sender}: {merged_message}"
                    )
                    send_whatsapp_message(
                        sender,
                        "⚠ Invalid selection. Please enter a number between 1-5.",
                    )
                    return None

        if (
            sender in user_sessions
            and user_sessions[sender].get("status") == "awaiting_confirmation"
        ):
            logger.info(f"Handling confirmation flow for {sender}")
            if merged_message == "proceed":
                current_team_name = user_sessions[sender]["current_team"]
                current_team_requests = [
                    entry
                    for entry in user_sessions[sender]["stored_requests"]
                    if entry["team_name"] == current_team_name
                ]
                response = await create_request_from_whatsapp(
                    sender, phone_number, current_team_requests
                )
                # Handle next team processing with auto-processing support
                from app.teams.handle_team_request import handle_auto_processing

                if sender in user_sessions and user_sessions[sender].get(
                    "pending_teams"
                ):
                    result = process_next_team(sender, user_sessions)
                    if result == "awaiting_confirmation":
                        user_sessions[sender]["status"] = "awaiting_confirmation"
                    elif result == "auto_process":
                        await handle_auto_processing(sender, user_sessions, result)
                return None
            elif merged_message == "cancel":
                logger.info(f"{sender} requested to change team selection")
                if (
                    "current_team" not in user_sessions[sender]
                    or not user_sessions[sender]["current_team"]
                ):
                    send_whatsapp_message(
                        sender, "⚠ No team is currently being reviewed."
                    )
                    return None

                current_team_name = user_sessions[sender]["current_team"]
                original_team_name = next(
                    (
                        orig_name
                        for orig_name, data in user_sessions[sender][
                            "team_name_updates"
                        ].items()
                        if data.get("new_team_name") == current_team_name
                    ),
                    current_team_name,
                )
                team_name_updates = user_sessions[sender].get("team_name_updates", {})
                lookup_options = team_name_updates.get(original_team_name, {}).get(
                    "top_teams", []
                )

                if not lookup_options:
                    send_whatsapp_message(
                        sender, f"⚠ No alternative teams found for {current_team_name}."
                    )
                    return None

                formatted_options = "\n".join(
                    [
                        f"{i} - {team['name']} ({team['area_name']}, {ordinal_superscript(team['division_level'])} division)"
                        for i, team in enumerate(lookup_options, start=1)
                    ]
                )
                send_whatsapp_message(
                    sender,
                    f"🔄 *Top 5 Alternative Teams for {current_team_name}:*\n{formatted_options}\n\n"
                    "Reply with a number (1-5) to select an alternative team.",
                )
                user_sessions[sender]["awaiting_team_selection"] = True
                return None

        # Handle player update confirmation responses
        if (
            sender in user_sessions
            and user_sessions[sender].get("type") == "player_update"
        ):
            session_status = user_sessions[sender].get("status")

            if session_status == "awaiting_confirmation":
                logger.info(f"Handling player update confirmation for {sender}")
                # Only handle specific template button payloads
                if merged_message in [
                    "proceed_update_player",
                    "change_player",
                    "change_contact",
                ]:
                    await handle_player_update_confirmation(
                        sender, merged_message, user_sessions
                    )
                    return None
                else:
                    # Clear session and allow new conversation to start
                    logger.info(
                        f"Player update session cleared for {sender} - starting new conversation"
                    )
                    user_sessions[sender] = {}

            elif session_status == "awaiting_player_selection":
                logger.info(f"Handling player selection for {sender}")
                # Only handle numeric selections
                if merged_message.isdigit():
                    await handle_player_selection(sender, merged_message, user_sessions)
                    return None
                else:
                    # Clear session and allow new conversation to start
                    logger.info(
                        f"Player update session cleared for {sender} - starting new conversation"
                    )
                    user_sessions[sender] = {}

            elif session_status == "awaiting_contact_selection":
                logger.info(f"Handling contact selection for {sender}")
                # Only handle numeric selections
                if merged_message.isdigit():
                    await handle_contact_selection(
                        sender, merged_message, user_sessions
                    )
                    return None
                else:
                    # Clear session and allow new conversation to start
                    logger.info(
                        f"Player update session cleared for {sender} - starting new conversation"
                    )
                    user_sessions[sender] = {}

        # Handle activity confirmation responses
        if sender in user_sessions and user_sessions[sender].get("type") == "activity":
            session_status = user_sessions[sender].get("status")

            if session_status == "awaiting_confirmation":
                logger.info(f"Handling activity confirmation for {sender}")
                # Handle template button payloads
                if merged_message in [
                    "proceed_create_activity",
                    "change_player_activity",
                    "change_team_activity",
                ]:
                    await handle_activity_confirmation(
                        sender, merged_message, user_sessions
                    )
                    return None
                else:
                    # Clear session and allow new conversation to start
                    logger.info(
                        f"Activity session cleared for {sender} - starting new conversation"
                    )
                    user_sessions[sender] = {}

            elif session_status == "awaiting_player_selection":
                logger.info(f"Handling activity player selection for {sender}")
                # Only handle numeric selections
                if merged_message.isdigit():
                    await handle_activity_player_selection(
                        sender, merged_message, user_sessions
                    )
                    return None
                else:
                    # Clear session and allow new conversation to start
                    logger.info(
                        f"Activity session cleared for {sender} - starting new conversation"
                    )
                    user_sessions[sender] = {}

            elif session_status == "awaiting_team_selection":
                logger.info(f"Handling activity team selection for {sender}")
                # Only handle numeric selections
                if merged_message.isdigit():
                    await handle_activity_team_selection(
                        sender, merged_message, user_sessions
                    )
                    return None
                else:
                    # Clear session and allow new conversation to start
                    logger.info(
                        f"Activity session cleared for {sender} - starting new conversation"
                    )
                    user_sessions[sender] = {}

        # Use router agent to classify and route the message
        logger.info(f"🧭 Routing message from {sender} through RouterAgent")
        send_whatsapp_message(sender, "Processing your message, please wait...")

        # ⏱️ Time router processing
        router_start = time.perf_counter()
        try:
            routing_result = await router_agent.route_message(merged_message, sender)
            router_time = time.perf_counter() - router_start
            logger.info(f"🧭 Router processing completed in {router_time:.4f} sec")

            # Router decision is now automatically traced via @observe decorator

            if routing_result.get("success", False):
                # Successfully routed - execute the handler
                handler_name = routing_result["handler"]
                function_args = routing_result["function_args"]

                logger.info(
                    f"Router selected handler: {handler_name} with confidence: {function_args.get('confidence', 'N/A')}"
                )

                # Execute handler with tracing
                logger.info(f"🎯 Executing handler: {handler_name}")

                # Ensure function_args has required parameters as fallback
                if "sender" not in function_args:
                    logger.warning(
                        f"Adding missing 'sender' to function_args for {handler_name}"
                    )
                    function_args["sender"] = sender

                if "message" not in function_args:
                    logger.warning(
                        f"Adding missing 'message' to function_args for {handler_name}"
                    )
                    function_args["message"] = merged_message

                handler_start_time = time.perf_counter()
                try:
                    result = await handler_registry.execute_handler(
                        handler_name,
                        function_args,
                        client=ai_client,
                        user_sessions=user_sessions,
                        trace_id=thread_id,
                    )

                    handler_duration = time.perf_counter() - handler_start_time
                    logger.info(
                        f"🎯 Handler {handler_name} completed in {handler_duration:.4f} sec"
                    )

                    # Handler execution is now automatically traced via @observe decorator

                except Exception as handler_error:
                    # Handler execution errors are now automatically traced via @observe decorator
                    raise

            else:
                # Routing failed - handle error
                logger.warning(
                    f"Router failed to classify message from {sender}: {routing_result.get('error_type', 'unknown')}"
                )

                # Send error message to user
                error_message = routing_result.get(
                    "error_message",
                    "I couldn't understand your request. Please try rephrasing your message or contact support for assistance.",
                )
                send_whatsapp_message(sender, error_message)

        except Exception as e:
            logger.error(f"Error in routing process: {e}")
            raise

        # ⏱️ Log total processing time
        if webhook_start_time:
            total_processing_time = time.perf_counter() - webhook_start_time
            logger.info(
                f"🏆 TOTAL MESSAGE PROCESSING TIME: {total_processing_time:.4f} sec"
            )

        final_processing_time = time.perf_counter() - final_processing_start
        logger.info(
            f"🔄 Final message processing completed in {final_processing_time:.4f} sec"
        )

    except Exception as e:
        logger.error(f"Error in process_final_message: {e}")
        raise
