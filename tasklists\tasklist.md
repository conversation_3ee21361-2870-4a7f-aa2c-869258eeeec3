# WhatsApp Bot Improvement Tasklist

## AI Model Upgrades
- [ ] Update OpenAI model to latest available version (GPT-4 or newer)
- [ ] Test improved club name recognition for problematic cases:
  - [ ] Bodø/Glimt
  - [ ] Aalborg
  - [ ] FC Copenhagen
- [ ] Evaluate if model upgrade resolves team name recognition issues or if additional logic is needed

## Team Request Enhancements
- [x] Add logic to recognize and extract club contact person information
- [x] Update `handle_team_request.py` to include contact person in the request payload
- [x] Modify database schema if needed to store contact person information
- [x] Update confirmation messages to include contact person details

## Player Request Improvements
- [ ] Add functionality to recognize and process player control stage information
- [ ] Update `handle_player_request.py` to extract control stage from user messages
- [ ] Include control stage in player creation API payload
- [ ] Add validation for control stage values
- [ ] Update confirmation messages to include control stage information

## General Improvements
- [ ] Enhance error handling for API failures
- [ ] Improve message formatting for better readability
- [ ] Add more detailed logging for troubleshooting
- [ ] Implement unit tests for new functionality
