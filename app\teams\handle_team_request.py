from app.config import settings
import time
from app.utils import get_async_response, get_absolute_path
from app.utils.team_selection import select_best_team_match
from app.utils.whatsapp_handler import (
    send_whatsapp_message,
)
import json
import re
from .process_next_team import process_next_team
import requests
import asyncio
from app.db import insert_whatsapp_request
import time
import logging
from langfuse import observe
from app.monitoring.whatsapp_tracer import trace_assistant, trace_response, trace_error
from app.utils.ai_client import get_ai_client

logging.basicConfig(level=logging.INFO)


def _load_system_instructions():
    """Load system instructions from file."""
    try:
        instructions_path = get_absolute_path("app/teams/system_instructions.txt")
        with open(instructions_path, "r", encoding="utf-8") as f:
            return f.read().strip()
    except FileNotFoundError:
        raise FileNotFoundError("system_instructions.txt not found")


def _clean_json_content(content):
    """Clean JSON content by removing/normalizing problematic characters."""
    if not content:
        return content

    # Replace multiple consecutive whitespace characters with single spaces
    # This handles tabs, multiple spaces, and other whitespace issues
    cleaned = re.sub(r"\s+", " ", content)

    # Remove any control characters except newlines, tabs, and carriage returns
    # Keep only printable characters and basic whitespace
    cleaned = re.sub(r"[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]", "", cleaned)

    return cleaned.strip()


async def handle_auto_processing(sender, user_sessions, process_result):
    """Handle auto-processing for exact matches and continue with next teams."""
    if process_result == "auto_process" and sender in user_sessions:
        from app.utils.create_requests_from_whatsapp import create_request_from_whatsapp
        from app.utils.whatsapp_handler import send_whatsapp_message

        # Get phone number from sender
        phone_number = sender.split(":")[1]

        # Get the current team requests
        team_requests = user_sessions[sender].get("current_team_requests", [])

        try:
            # Process the current team request
            await create_request_from_whatsapp(sender, phone_number, team_requests)

            # Continue processing the next team if there are more teams
            if user_sessions[sender]["pending_teams"]:
                logging.info(
                    f"🔄 Continuing to next team. Remaining teams: {len(user_sessions[sender]['pending_teams'])}"
                )
                result = process_next_team(sender, user_sessions)

                # Update session status based on the result
                if result == "awaiting_confirmation":
                    user_sessions[sender]["status"] = "awaiting_confirmation"
                    logging.info(
                        f"✅ Next team needs confirmation. Session status updated to awaiting_confirmation"
                    )
                elif result == "auto_process":
                    # Recursively handle auto-processing for the next team
                    await handle_auto_processing(sender, user_sessions, result)
            else:
                logging.info("✅ All teams processed successfully!")
                # Clear the session when all teams are processed
                if sender in user_sessions:
                    del user_sessions[sender]

        except Exception as e:
            send_whatsapp_message(sender, f"⚠️ Error processing request: {str(e)}")
            logging.error(f"Error in auto-processing: {e}")


@observe(name="Team Handler", as_type="generation")
async def handle_team_request(
    sender, incoming_msg, client, user_sessions, trace_id=None
):
    """
    Handles team-related requests.
    """
    start_time = time.perf_counter()

    try:
        insert_whatsapp_request(sender, incoming_msg)
    except Exception as e:
        print("Error saving the player log", e)

    # ✅ Step 1: Load system instructions
    system_instructions = _load_system_instructions()

    # ✅ Step 2: AI Text Extraction (Gemini/OpenAI call)
    from langfuse import get_client

    langfuse = get_client()
    ai_client = get_ai_client()

    with langfuse.start_as_current_generation(
        name="AI Text Extraction",
        model="gemini-2.5-flash-lite-preview-06-17",
        input={
            "message": incoming_msg,
            "system_instructions": system_instructions[:200],
        },
        metadata={"component": "team_handler", "step": "ai_extraction"},
    ) as ai_span:
        chat_start = time.perf_counter()

        try:
            ai_response = await ai_client.chat_completion(
                messages=[
                    {"role": "system", "content": system_instructions},
                    {"role": "user", "content": incoming_msg},
                ],
                model="gemini-2.5-flash-lite-preview-06-17",
                temperature=0.01,
                max_tokens=10000,
                response_format={"type": "json_object"},  # Ensures JSON output
            )

            assistant_duration = time.perf_counter() - chat_start
            logging.info(
                f"🕒 AI processing took {assistant_duration:.4f} sec using {ai_response.provider.value}"
            )

            # Parse the response with better debugging
            ai_reply = None
            if ai_response.content and ai_response.content.strip():
                try:
                    # Clean the content before parsing to remove control characters
                    cleaned_content = _clean_json_content(ai_response.content.strip())
                    ai_reply = json.loads(cleaned_content)
                except json.JSONDecodeError as e:
                    logging.error(f"Failed to parse JSON response: {e}")
                    logging.error(f"Raw content that failed: '{ai_response.content}'")
                    logging.error(
                        f"Cleaned content that failed: '{_clean_json_content(ai_response.content.strip())}'"
                    )
                    ai_reply = {"error": f"Invalid AI response format: {e}"}
            else:
                logging.error(
                    f"Empty or None response content from {ai_response.provider.value}"
                )
                logging.error(f"Full response object: {ai_response}")
                ai_reply = {
                    "error": f"No response received from {ai_response.provider.value}"
                }

            # Update AI span with results
            ai_span.update(
                output=ai_reply,
                usage_details={
                    "input_tokens": (
                        ai_response.usage["prompt_tokens"] if ai_response.usage else 0
                    ),
                    "output_tokens": (
                        ai_response.usage["completion_tokens"]
                        if ai_response.usage
                        else 0
                    ),
                    "total_tokens": (
                        ai_response.usage["total_tokens"] if ai_response.usage else 0
                    ),
                },
            )

        except Exception as e:
            logging.error(f"AI API call failed: {e}")
            ai_span.update(output={"error": str(e)})
            send_whatsapp_message(
                sender,
                "⚠️ Sorry, I encountered an error processing your request. Please try again.",
            )
            return

    # Trace assistant call
    if trace_id:
        try:
            await trace_assistant(
                trace_id,
                f"chat-completions-{ai_response.model}",  # Updated to reflect actual model used
                incoming_msg,
                {
                    "response_received": ai_reply is not None,
                    "has_results": bool(ai_reply and ai_reply.get("result")),
                    "model": ai_response.model,
                    "provider": ai_response.provider.value,
                    "tokens_used": (
                        ai_response.usage["total_tokens"] if ai_response.usage else None
                    ),
                },
                assistant_duration,
            )
        except Exception as e:
            logging.warning(f"Failed to trace assistant call: {e}")

    if not ai_reply or isinstance(ai_reply, str):
        ai_reply = {
            "error": "Sorry, I couldn't process your request. Please try again."
        }

    if not ai_reply.get("result"):
        error_msg = (
            "⚠️ Sorry, I couldn't find any teams in the message. Please try again."
        )
        send_whatsapp_message(sender, error_msg)

        # Trace error response
        if trace_id:
            try:
                await trace_response(trace_id, sender, error_msg, False)
            except Exception as e:
                logging.warning(f"Failed to trace error response: {e}")
        return

    # ✅ Step 3: Lookup Call (API lookup + response compilation)
    with langfuse.start_as_current_span(
        name="Lookup Call",
        input={"teams_to_lookup": ai_reply.get("result", [])},
        metadata={"component": "team_handler", "step": "lookup_and_compile"},
    ) as lookup_span:
        lookup_start = time.perf_counter()
        team_name_updates = {}
        processed_teams = {}
        error_occurred = False

        for entry in ai_reply.get("result", []):
            team_name = entry["team_name"]
            if team_name in processed_teams:
                logging.info(
                    f"⚡ Skipping API lookup for {team_name}, using cached data."
                )
                entry["team_name"] = processed_teams[team_name]["new_team_name"]
                entry["teamId"] = processed_teams[team_name]["teamId"]
                continue  # Move to the next entry

            try:
                api_start = time.perf_counter()
                resp = await get_async_response(
                    f"{settings.LOOKUP_API_URL}/teams/updated_lookup/{team_name}",
                    auth=(settings.LOOKUP_API_USR, settings.LOOKUP_API_PASS),
                )
                logging.info(
                    f"🌍 API lookup for {team_name} took {time.perf_counter() - api_start:.4f} sec"
                )

                if resp.is_error:
                    send_whatsapp_message(
                        sender,
                        f"⚠️ Sorry, I couldn't find any matching teams for '{team_name}'. ",
                    )
                    error_occurred = True
                    break
                else:
                    filtered_teams = resp.json()

                    if filtered_teams:
                        # Use the team selection utility for consistent logic
                        best_match, skip_confirmation, sorted_alternatives = (
                            select_best_team_match(filtered_teams, team_name)
                        )

                        processed_teams[team_name] = {
                            "new_team_name": best_match["name"],
                            "teamId": best_match["teamId"],
                            "area_name": best_match.get("area_name", "Unknown"),
                            "top_teams": sorted_alternatives[:5],
                            "skip_confirmation": skip_confirmation,
                        }

                        team_name_updates[team_name] = processed_teams[team_name]

                        entry["team_name"] = best_match["name"]
                        entry["teamId"] = best_match["teamId"]
                        entry["area_name"] = best_match.get("area_name", "Unknown")
                        entry["skip_confirmation"] = skip_confirmation
                    else:
                        send_whatsapp_message(
                            sender,
                            f"⚠️ Sorry, I couldn't find any matching teams for '{team_name}'. ",
                        )
                        error_occurred = True
                        break

            except requests.RequestException as e:
                send_whatsapp_message(
                    sender, f"❌ Failed to fetch lookup for {team_name}"
                )
                logging.error(f"❌ Failed to fetch lookup for {team_name}: {str(e)}")
                error_occurred = True
                break

        logging.info(
            f"🔍 Team lookup and processing took {time.perf_counter() - lookup_start:.4f} sec"
        )

        if error_occurred:
            lookup_span.update(
                output={"error": "Team lookup failed", "error_occurred": True}
            )
            return

        # ✅ Step 4: Update User Session
        session_update_start = time.perf_counter()
        user_sessions[sender]["json_data"] = ai_reply
        user_sessions[sender]["status"] = "awaiting_confirmation"
        user_sessions[sender]["pending_teams"] = ai_reply.get("result", [])
        user_sessions[sender]["team_name_updates"] = team_name_updates
        user_sessions[sender]["lookup_options"] = {}

        # Extract contact person information if available
        for team in user_sessions[sender]["pending_teams"]:
            team_name = team["team_name"]
            # Add contact_person field if it exists in AI reply
            if "contact_person" in team:
                logging.info(
                    f"Contact person found for {team_name}: {team['contact_person']}"
                )
            else:
                team["contact_person"] = None

            if team_name in team_name_updates:
                team["team_name"] = team_name_updates[team_name]["new_team_name"]
                team["teamId"] = team_name_updates[team_name]["teamId"]
                team["area_name"] = team_name_updates[team_name]["area_name"]

        logging.info(
            f"🗂 User session update took {time.perf_counter() - session_update_start:.4f} sec"
        )

        # ✅ Step 5: Process Next Team with auto-processing support
        process_start = time.perf_counter()
        result = process_next_team(sender, user_sessions)
        logging.info(
            f"🚀 process_next_team took {time.perf_counter() - process_start:.4f} sec"
        )

        # ✅ Handle auto-processing for exact matches
        await handle_auto_processing(sender, user_sessions, result)

        # Update lookup span with results
        lookup_span.update(
            output={
                "teams_processed": len(ai_reply.get("result", [])),
                "team_name_updates": team_name_updates,
                "session_updated": True,
                "next_team_processed": True,
            }
        )

    # ✅ Final Logging
    logging.info(
        f"✅ Total handle_team_request execution time: {time.perf_counter() - start_time:.4f} sec"
    )
