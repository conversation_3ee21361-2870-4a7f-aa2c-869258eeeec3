import re

def is_player_request(message: str) -> bool:
    """
    Determines if a WhatsApp message is a player request.
    A player request is identified if it contains a Transfermarkt URL.
    """
    transfermarkt_pattern = r"https?://(www\.)?transfermarkt\.(com|com.ar|co.uk|co|de|it|fr|es|nl|ru|pt|pl|tr|gr|cz|ro|hu|ua|br|ar|mx|ch|at|dk|se|no|fi|be|hr|rs|bg|sk|si|ba|ie|lu|mt|cy|lt|lv|ee|us|ar)/[^\s]+"
    return bool(re.search(transfermarkt_pattern, message))
