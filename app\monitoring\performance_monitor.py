import time
import logging
import json
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from collections import defaultdict, deque
from dataclasses import dataclass, asdict
from .langfuse_setup import log_router_decision, log_handler_execution

logger = logging.getLogger(__name__)


@dataclass
class RouterDecision:
    """Data class for router decision metrics."""

    timestamp: float
    sender: str
    message_preview: str
    function_name: str
    confidence: float
    reasoning: str
    execution_time: float
    success: bool


@dataclass
class HandlerExecution:
    """Data class for handler execution metrics."""

    timestamp: float
    handler_name: str
    sender: str
    execution_time: float
    success: bool
    error_message: Optional[str] = None


class PerformanceMonitor:
    """
    Comprehensive performance monitoring for the WhatsApp bot router system.

    Tracks router decisions, handler executions, and provides analytics.
    """

    def __init__(self, max_history: int = 1000):
        """
        Initialize the performance monitor.

        Args:
            max_history: Maximum number of records to keep in memory
        """
        self.max_history = max_history

        # Router decision tracking
        self.router_decisions: deque = deque(maxlen=max_history)
        self.router_stats = {
            "total_decisions": 0,
            "successful_decisions": 0,
            "failed_decisions": 0,
            "average_confidence": 0.0,
            "average_execution_time": 0.0,
        }

        # Handler execution tracking
        self.handler_executions: deque = deque(maxlen=max_history)
        self.handler_stats = defaultdict(
            lambda: {
                "total_executions": 0,
                "successful_executions": 0,
                "failed_executions": 0,
                "average_execution_time": 0.0,
                "total_execution_time": 0.0,
                "last_execution": None,
                "error_rate": 0.0,
            }
        )

        # Function usage tracking
        self.function_usage = defaultdict(int)

        logger.info("PerformanceMonitor initialized")

    def track_router_decision(
        self,
        sender: str,
        message: str,
        function_name: str,
        confidence: float,
        reasoning: str,
        execution_time: float,
        success: bool = True,
    ):
        """
        Track a router decision for monitoring and analysis.

        Args:
            sender: Sender identifier
            message: Original message
            function_name: Selected function name
            confidence: Router confidence score
            reasoning: Router reasoning
            execution_time: Time taken for decision
            success: Whether the decision was successful
        """
        decision = RouterDecision(
            timestamp=time.time(),
            sender=sender,
            message_preview=message[:100],
            function_name=function_name,
            confidence=confidence,
            reasoning=reasoning,
            execution_time=execution_time,
            success=success,
        )

        self.router_decisions.append(decision)
        self.function_usage[function_name] += 1

        # Update router statistics
        self.router_stats["total_decisions"] += 1
        if success:
            self.router_stats["successful_decisions"] += 1
        else:
            self.router_stats["failed_decisions"] += 1

        # Update averages
        total = self.router_stats["total_decisions"]
        self.router_stats["average_confidence"] = (
            self.router_stats["average_confidence"] * (total - 1) + confidence
        ) / total
        self.router_stats["average_execution_time"] = (
            self.router_stats["average_execution_time"] * (total - 1) + execution_time
        ) / total

        # Log to LangFuse
        log_router_decision(
            message, sender, function_name, confidence, reasoning, execution_time
        )

        logger.info(
            f"Router decision tracked: {function_name} (confidence: {confidence:.2f})"
        )

    def track_handler_execution(
        self,
        handler_name: str,
        sender: str,
        execution_time: float,
        success: bool,
        error_message: Optional[str] = None,
    ):
        """
        Track a handler execution for performance monitoring.

        Args:
            handler_name: Name of the executed handler
            sender: Sender identifier
            execution_time: Handler execution time
            success: Whether execution was successful
            error_message: Error message if execution failed
        """
        execution = HandlerExecution(
            timestamp=time.time(),
            handler_name=handler_name,
            sender=sender,
            execution_time=execution_time,
            success=success,
            error_message=error_message,
        )

        self.handler_executions.append(execution)

        # Update handler statistics
        stats = self.handler_stats[handler_name]
        stats["total_executions"] += 1
        stats["total_execution_time"] += execution_time
        stats["last_execution"] = time.time()

        if success:
            stats["successful_executions"] += 1
        else:
            stats["failed_executions"] += 1

        # Update averages and error rate
        total = stats["total_executions"]
        stats["average_execution_time"] = stats["total_execution_time"] / total
        stats["error_rate"] = (stats["failed_executions"] / total) * 100

        # Log to LangFuse
        log_handler_execution(
            handler_name, sender, execution_time, success, error_message
        )

        logger.info(
            f"Handler execution tracked: {handler_name} ({'success' if success else 'failed'})"
        )

    def get_router_summary(self) -> Dict[str, Any]:
        """Get summary of router performance."""
        return {
            "statistics": self.router_stats.copy(),
            "function_usage": dict(self.function_usage),
            "recent_decisions": len(self.router_decisions),
            "success_rate": (
                (
                    self.router_stats["successful_decisions"]
                    / self.router_stats["total_decisions"]
                    * 100
                )
                if self.router_stats["total_decisions"] > 0
                else 0
            ),
        }

    def get_handler_summary(self) -> Dict[str, Any]:
        """Get summary of handler performance."""
        return {
            "handlers": dict(self.handler_stats),
            "total_executions": sum(
                stats["total_executions"] for stats in self.handler_stats.values()
            ),
            "recent_executions": len(self.handler_executions),
        }

    def get_recent_activity(self, minutes: int = 60) -> Dict[str, Any]:
        """
        Get recent activity within the specified time window.

        Args:
            minutes: Time window in minutes

        Returns:
            Dictionary with recent activity data
        """
        cutoff_time = time.time() - (minutes * 60)

        recent_decisions = [
            decision
            for decision in self.router_decisions
            if decision.timestamp > cutoff_time
        ]

        recent_executions = [
            execution
            for execution in self.handler_executions
            if execution.timestamp > cutoff_time
        ]

        return {
            "time_window_minutes": minutes,
            "router_decisions": len(recent_decisions),
            "handler_executions": len(recent_executions),
            "decisions": [asdict(d) for d in recent_decisions[-10:]],  # Last 10
            "executions": [asdict(e) for e in recent_executions[-10:]],  # Last 10
        }

    def get_performance_alerts(self) -> List[Dict[str, Any]]:
        """
        Get performance alerts based on thresholds.

        Returns:
            List of alert dictionaries
        """
        alerts = []

        # Router performance alerts
        if self.router_stats["total_decisions"] > 10:
            success_rate = (
                self.router_stats["successful_decisions"]
                / self.router_stats["total_decisions"]
                * 100
            )
            if success_rate < 90:
                alerts.append(
                    {
                        "type": "router_success_rate",
                        "severity": "warning" if success_rate > 80 else "critical",
                        "message": f"Router success rate is {success_rate:.1f}%",
                        "value": success_rate,
                    }
                )

        # Handler performance alerts
        for handler_name, stats in self.handler_stats.items():
            if stats["total_executions"] > 5:
                if stats["error_rate"] > 20:
                    alerts.append(
                        {
                            "type": "handler_error_rate",
                            "severity": (
                                "warning" if stats["error_rate"] < 50 else "critical"
                            ),
                            "message": f"Handler {handler_name} error rate is {stats['error_rate']:.1f}%",
                            "handler": handler_name,
                            "value": stats["error_rate"],
                        }
                    )

                if stats["average_execution_time"] > 30:  # 30 seconds threshold
                    alerts.append(
                        {
                            "type": "handler_slow_execution",
                            "severity": "warning",
                            "message": f"Handler {handler_name} average execution time is {stats['average_execution_time']:.1f}s",
                            "handler": handler_name,
                            "value": stats["average_execution_time"],
                        }
                    )

        return alerts

    def export_metrics(self) -> Dict[str, Any]:
        """
        Export all metrics for external monitoring systems.

        Returns:
            Complete metrics dictionary
        """
        return {
            "timestamp": time.time(),
            "router": self.get_router_summary(),
            "handlers": self.get_handler_summary(),
            "recent_activity": self.get_recent_activity(60),
            "alerts": self.get_performance_alerts(),
        }

    def reset_metrics(self):
        """Reset all metrics (useful for testing or periodic cleanup)."""
        self.router_decisions.clear()
        self.handler_executions.clear()
        self.router_stats = {
            "total_decisions": 0,
            "successful_decisions": 0,
            "failed_decisions": 0,
            "average_confidence": 0.0,
            "average_execution_time": 0.0,
        }
        self.handler_stats.clear()
        self.function_usage.clear()

        logger.info("Performance metrics reset")


# Global performance monitor instance
performance_monitor = PerformanceMonitor()
