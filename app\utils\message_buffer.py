import asyncio
from collections import defaultdict
from langfuse import observe

# Dictionary to store user messages and timers
message_buffers = defaultdict(lambda: {"messages": [], "timer": None})


@observe(name="Message Buffering")
async def add_message_to_buffer(sender, message, callback):
    """
    Adds an incoming message to the buffer and starts a timer if not already running.
    After 3 seconds, calls the callback with the merged message.
    """

    # Append the new message to the user's buffer
    message_buffers[sender]["messages"].append(message)

    if message_buffers[sender]["timer"] is None:
        # Start a new timer
        message_buffers[sender]["timer"] = asyncio.create_task(
            _process_buffer(sender, callback)
        )


async def _process_buffer(sender, callback):
    """
    Waits for 1 second, then processes all buffered messages.
    """
    await asyncio.sleep(1)  # Wait time

    # Merge all messages in the buffer
    merged_message = " ".join(message_buffers[sender]["messages"]).strip()

    # Clear buffer and reset timer
    message_buffers[sender] = {"messages": [], "timer": None}

    # Call the provided callback with the merged message
    await callback(sender, merged_message)
