"""
Unified AI client wrapper that supports both Gemini and OpenAI APIs with fallback logic.
Provides a consistent interface for all AI operations in the WhatsApp bot.
"""

import os
import json
import logging
import time
import asyncio
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
from enum import Enum

import google.generativeai as genai
from openai import OpenAI
from langfuse import observe

logger = logging.getLogger(__name__)


class AIProvider(Enum):
    GEMINI = "gemini"
    OPENAI = "openai"


@dataclass
class AIResponse:
    """Standardized response format for both providers"""

    content: str
    provider: AIProvider
    model: str
    usage: Optional[Dict[str, int]] = None
    function_call: Optional[Dict[str, Any]] = None
    raw_response: Optional[Any] = None


@dataclass
class FunctionCall:
    """Standardized function call format"""

    name: str
    arguments: Dict[str, Any]


class UnifiedAIClient:
    """
    Unified client that uses Gemini as primary and OpenAI as fallback.
    Provides consistent interface for chat completions and function calling.
    """

    def __init__(self):
        """Initialize both Gemini and OpenAI clients"""
        # Initialize Gemini
        self.gemini_api_key = os.getenv("GEMINI_API_KEY")
        if self.gemini_api_key:
            genai.configure(api_key=self.gemini_api_key)

            self.gemini_client = genai.GenerativeModel(
                "gemini-2.5-flash-lite-preview-06-17"
            )
            logger.info("Gemini client initialized successfully")
        else:
            self.gemini_client = None
            logger.warning("GEMINI_API_KEY not found, Gemini client not initialized")

        # Initialize OpenAI as fallback
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        if self.openai_api_key:
            self.openai_client = OpenAI(api_key=self.openai_api_key)
            logger.info("OpenAI client initialized as fallback")
        else:
            self.openai_client = None
            logger.warning("OPENAI_API_KEY not found, no fallback available")

        # Configuration
        self.max_retries = 2
        self.retry_delay = 1.0

    def _convert_openai_messages_to_gemini(self, messages: List[Dict[str, str]]) -> str:
        """Convert OpenAI message format to Gemini prompt format"""
        prompt_parts = []

        for message in messages:
            role = message.get("role", "")
            content = message.get("content", "")

            if role == "system":
                prompt_parts.append(f"System: {content}")
            elif role == "user":
                prompt_parts.append(f"User: {content}")
            elif role == "assistant":
                prompt_parts.append(f"Assistant: {content}")

        return "\n\n".join(prompt_parts)

    def _convert_openai_functions_to_gemini(
        self, functions: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Convert OpenAI function format to Gemini function format"""
        gemini_functions = []

        for func in functions:
            gemini_func = {
                "name": func["name"],
                "description": func["description"],
                "parameters": func["parameters"],
            }
            gemini_functions.append(gemini_func)

        return gemini_functions

    @observe(name="Gemini API Call", as_type="generation")
    async def _call_gemini(
        self,
        messages: List[Dict[str, str]],
        model: str = "gemini-2.5-flash-lite-preview-06-17",
        temperature: float = 0.1,
        max_tokens: int = 1500,
        functions: Optional[List[Dict[str, Any]]] = None,
        response_format: Optional[Dict[str, str]] = None,
    ) -> AIResponse:
        """Call Gemini API with error handling"""
        try:
            # Convert messages to Gemini format
            prompt = self._convert_openai_messages_to_gemini(messages)

            # Remove duplicate generation_config definition

            # Handle JSON response format - use proper Gemini structured output
            generation_config = {
                "temperature": temperature,
                "max_output_tokens": max_tokens,
            }

            if response_format and response_format.get("type") == "json_object":
                generation_config["response_mime_type"] = "application/json"

            # Handle function calling - use proper Gemini format
            tools = None
            if functions:
                gemini_functions = self._convert_openai_functions_to_gemini(functions)
                tools = [{"function_declarations": gemini_functions}]

            # Define safety settings for each call
            safety_settings = [
                {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_NONE"},
                {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_NONE"},
                {
                    "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                    "threshold": "BLOCK_NONE",
                },
                {
                    "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
                    "threshold": "BLOCK_NONE",
                },
            ]

            # Make the API call with safety settings
            if tools:
                response = self.gemini_client.generate_content(
                    prompt,
                    generation_config=generation_config,
                    tools=tools,
                    tool_config={
                        "function_calling_config": {"mode": "ANY"}
                    },  # Enable constrained decoding
                    safety_settings=safety_settings,
                )
            else:
                response = self.gemini_client.generate_content(
                    prompt,
                    generation_config=generation_config,
                    safety_settings=safety_settings,
                )

            # Process response with better error handling
            content = ""
            function_call = None

            if response.candidates and len(response.candidates) > 0:
                candidate = response.candidates[0]

                # Check finish reason for safety filters
                if hasattr(candidate, "finish_reason") and candidate.finish_reason == 2:
                    raise Exception(
                        "Content blocked by Gemini safety filters - will fallback to OpenAI"
                    )

                # Extract content and function calls
                if (
                    hasattr(candidate, "content")
                    and candidate.content
                    and candidate.content.parts
                ):
                    for part in candidate.content.parts:
                        if hasattr(part, "text") and part.text:
                            content += part.text
                        elif hasattr(part, "function_call") and part.function_call:
                            function_call = {
                                "name": part.function_call.name,
                                "arguments": dict(part.function_call.args),
                            }
                            break  # Only take the first function call

                # Fallback: try direct text access if no content found and no function call
                if not content and not function_call:
                    try:
                        if hasattr(response, "text") and response.text:
                            content = response.text
                    except Exception as e:
                        logger.warning(f"Failed to access response.text: {e}")
                        # If response.text fails, content remains empty
                        pass

            # Extract usage information
            usage = None
            if hasattr(response, "usage_metadata") and response.usage_metadata:
                usage = {
                    "prompt_tokens": response.usage_metadata.prompt_token_count,
                    "completion_tokens": response.usage_metadata.candidates_token_count,
                    "total_tokens": response.usage_metadata.total_token_count,
                }

            return AIResponse(
                content=content,
                provider=AIProvider.GEMINI,
                model=model,
                usage=usage,
                function_call=function_call,
                raw_response=response,
            )

        except Exception as e:
            logger.error(f"Gemini API call failed: {e}")
            raise

    @observe(name="OpenAI API Call", as_type="generation")
    async def _call_openai(
        self,
        messages: List[Dict[str, str]],
        model: str = "gpt-4o-mini",
        temperature: float = 0.1,
        max_tokens: int = 1500,
        functions: Optional[List[Dict[str, Any]]] = None,
        response_format: Optional[Dict[str, str]] = None,
    ) -> AIResponse:
        """Call OpenAI API as fallback"""
        try:
            kwargs = {
                "model": model,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": max_tokens,
            }

            if functions:
                kwargs["functions"] = functions
                kwargs["function_call"] = "auto"

            if response_format:
                kwargs["response_format"] = response_format

            response = self.openai_client.chat.completions.create(**kwargs)

            # Extract content and function call
            message = response.choices[0].message
            content = message.content or ""

            function_call = None
            if message.function_call:
                function_call = {
                    "name": message.function_call.name,
                    "arguments": json.loads(message.function_call.arguments),
                }

            # Extract usage
            usage = None
            if response.usage:
                usage = {
                    "prompt_tokens": response.usage.prompt_tokens,
                    "completion_tokens": response.usage.completion_tokens,
                    "total_tokens": response.usage.total_tokens,
                }

            return AIResponse(
                content=content,
                provider=AIProvider.OPENAI,
                model=model,
                usage=usage,
                function_call=function_call,
                raw_response=response,
            )

        except Exception as e:
            logger.error(f"OpenAI API call failed: {e}")
            raise

    @observe(name="Unified AI Call", as_type="generation")
    async def chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: str = "gemini-2.5-flash-lite-preview-06-17",
        temperature: float = 0.1,
        max_tokens: int = 1500,
        functions: Optional[List[Dict[str, Any]]] = None,
        response_format: Optional[Dict[str, str]] = None,
        force_provider: Optional[AIProvider] = None,
    ) -> AIResponse:
        """
        Unified chat completion with automatic fallback.

        Args:
            messages: List of message dictionaries in OpenAI format
            model: Model name (will be mapped to appropriate provider model)
            temperature: Sampling temperature
            max_tokens: Maximum tokens to generate
            functions: Function definitions for function calling
            response_format: Response format specification
            force_provider: Force specific provider (for testing)

        Returns:
            AIResponse object with standardized format
        """
        # Determine provider order
        providers_to_try = []

        if force_provider:
            if force_provider == AIProvider.GEMINI and self.gemini_client:
                providers_to_try = [AIProvider.GEMINI]
            elif force_provider == AIProvider.OPENAI and self.openai_client:
                providers_to_try = [AIProvider.OPENAI]
        else:
            # Default: Try Gemini first, then OpenAI
            if self.gemini_client:
                providers_to_try.append(AIProvider.GEMINI)
            if self.openai_client:
                providers_to_try.append(AIProvider.OPENAI)

        if not providers_to_try:
            raise RuntimeError("No AI providers available")

        last_error = None

        for provider in providers_to_try:
            for attempt in range(self.max_retries):
                try:
                    logger.info(
                        f"Attempting {provider.value} API call (attempt {attempt + 1})"
                    )

                    if provider == AIProvider.GEMINI:
                        # Map model names for Gemini - use lite preview for optimal performance
                        gemini_model = (
                            "gemini-2.5-flash-lite-preview-06-17"  # Fastest 2.5 variant
                        )
                        response = await self._call_gemini(
                            messages=messages,
                            model=gemini_model,
                            temperature=temperature,
                            max_tokens=max_tokens,
                            functions=functions,
                            response_format=response_format,
                        )
                    else:  # OpenAI
                        openai_model = (
                            "gpt-4o-mini" if "mini" in model.lower() else "gpt-4o"
                        )
                        response = await self._call_openai(
                            messages=messages,
                            model=openai_model,
                            temperature=temperature,
                            max_tokens=max_tokens,
                            functions=functions,
                            response_format=response_format,
                        )

                    logger.info(f"Successfully called {provider.value} API")
                    return response

                except Exception as e:
                    last_error = e
                    if "safety filters" in str(e).lower():
                        logger.warning(
                            f"{provider.value} blocked content due to safety filters, trying fallback"
                        )
                    else:
                        logger.warning(
                            f"{provider.value} API call failed (attempt {attempt + 1}): {e}"
                        )

                    if attempt < self.max_retries - 1:
                        await asyncio.sleep(self.retry_delay)
                    continue

            logger.error(f"All attempts failed for {provider.value}")

        # If we get here, all providers failed
        raise RuntimeError(f"All AI providers failed. Last error: {last_error}")

    def get_available_providers(self) -> List[AIProvider]:
        """Get list of available providers"""
        providers = []
        if self.gemini_client:
            providers.append(AIProvider.GEMINI)
        if self.openai_client:
            providers.append(AIProvider.OPENAI)
        return providers

    def health_check(self) -> Dict[str, bool]:
        """Check health of all providers"""
        return {
            "gemini": self.gemini_client is not None,
            "openai": self.openai_client is not None,
        }


# Global instance
_ai_client = None


def get_ai_client() -> UnifiedAIClient:
    """Get global AI client instance"""
    global _ai_client
    if _ai_client is None:
        _ai_client = UnifiedAIClient()
    return _ai_client
