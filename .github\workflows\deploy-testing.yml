name: cloudrun-deploy-testing
on:
  push:
    branches:
      - testing
jobs:
  setup-build-publish-deploy:
    name: Setup, Build, Publish, and Deploy (Testing)
    runs-on: ubuntu-latest
    steps:
    - name: Checkout
      uses: actions/checkout@main
    
    - id: 'auth'
      uses: 'google-github-actions/auth@v0'
      with:
        credentials_json: '${{ secrets.GCP_CREDENTIALS }}'

    # Setup gcloud CLI
    - name: 'Set up Cloud SDK'
      uses: google-github-actions/setup-gcloud@v0
    
    # Configure Docker with Credentials
    - name: Configure Docker
      run: |
        gcloud auth configure-docker
      
    # Build the Docker image
    - name: Build & Publish
      run: |
        gcloud config set project ${{ secrets.GCP_PROJECT }}
        gcloud builds submit --gcs-source-staging-dir=gs://regional-crm-artifacts/containers/images/${{ secrets.GCP_APPLICATION_TESTING }} --tag europe-west1-docker.pkg.dev/${{ secrets.GCP_PROJECT }}/images/${{ secrets.GCP_APPLICATION_TESTING }}:$GITHUB_SHA
        gcloud config set run/region europe-west1
      
    # Deploy the Docker image to the GKE cluster
    - name: Deploy
      run: |
        gcloud run deploy ${{ secrets.GCP_APPLICATION_TESTING }} --image europe-west1-docker.pkg.dev/${{ secrets.GCP_PROJECT }}/images/${{ secrets.GCP_APPLICATION_TESTING }}:$GITHUB_SHA \
        --platform managed \
        --allow-unauthenticated \
        --region=europe-west1
