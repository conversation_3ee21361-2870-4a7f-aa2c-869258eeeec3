from fastapi import APIRouter, HTTPException
from typing import Dict, Any, Optional
import logging
from .performance_monitor import performance_monitor
from .langfuse_setup import validate_langfuse_config, get_monitoring_dashboard_url
from .whatsapp_tracer import whatsapp_tracer

logger = logging.getLogger(__name__)

# Create monitoring router
monitoring_router = APIRouter(prefix="/monitoring", tags=["monitoring"])


@monitoring_router.get("/health")
async def monitoring_health():
    """Health check for monitoring system."""
    langfuse_status = validate_langfuse_config()

    return {
        "status": "healthy",
        "monitoring_active": True,
        "langfuse": langfuse_status,
        "performance_monitor": {
            "total_decisions": performance_monitor.router_stats["total_decisions"],
            "total_executions": sum(
                stats["total_executions"]
                for stats in performance_monitor.handler_stats.values()
            ),
        },
    }


@monitoring_router.get("/router/summary")
async def get_router_summary():
    """Get router performance summary."""
    try:
        return performance_monitor.get_router_summary()
    except Exception as e:
        logger.error(f"Error getting router summary: {e}")
        raise HTTPException(status_code=500, detail="Failed to get router summary")


@monitoring_router.get("/handlers/summary")
async def get_handlers_summary():
    """Get handlers performance summary."""
    try:
        return performance_monitor.get_handler_summary()
    except Exception as e:
        logger.error(f"Error getting handlers summary: {e}")
        raise HTTPException(status_code=500, detail="Failed to get handlers summary")


@monitoring_router.get("/activity/recent")
async def get_recent_activity(minutes: int = 60):
    """
    Get recent activity within specified time window.

    Args:
        minutes: Time window in minutes (default: 60)
    """
    try:
        if minutes <= 0 or minutes > 1440:  # Max 24 hours
            raise HTTPException(
                status_code=400, detail="Minutes must be between 1 and 1440"
            )

        return performance_monitor.get_recent_activity(minutes)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting recent activity: {e}")
        raise HTTPException(status_code=500, detail="Failed to get recent activity")


@monitoring_router.get("/alerts")
async def get_performance_alerts():
    """Get current performance alerts."""
    try:
        return {
            "alerts": performance_monitor.get_performance_alerts(),
            "timestamp": performance_monitor.router_stats.get("last_update", None),
        }
    except Exception as e:
        logger.error(f"Error getting performance alerts: {e}")
        raise HTTPException(status_code=500, detail="Failed to get performance alerts")


@monitoring_router.get("/metrics/export")
async def export_all_metrics():
    """Export all metrics for external monitoring systems."""
    try:
        return performance_monitor.export_metrics()
    except Exception as e:
        logger.error(f"Error exporting metrics: {e}")
        raise HTTPException(status_code=500, detail="Failed to export metrics")


@monitoring_router.get("/dashboard")
async def get_dashboard_info():
    """Get monitoring dashboard information and links."""
    langfuse_status = validate_langfuse_config()
    dashboard_url = get_monitoring_dashboard_url()

    return {
        "langfuse": {
            "enabled": langfuse_status["enabled"],
            "connected": langfuse_status["connected"],
            "dashboard_url": dashboard_url,
            "host": langfuse_status.get("host"),
        },
        "api_endpoints": {
            "health": "/monitoring/health",
            "router_summary": "/monitoring/router/summary",
            "handlers_summary": "/monitoring/handlers/summary",
            "recent_activity": "/monitoring/activity/recent",
            "alerts": "/monitoring/alerts",
            "metrics_export": "/monitoring/metrics/export",
        },
        "performance_summary": {
            "total_router_decisions": performance_monitor.router_stats[
                "total_decisions"
            ],
            "total_handler_executions": sum(
                stats["total_executions"]
                for stats in performance_monitor.handler_stats.values()
            ),
            "active_handlers": len(performance_monitor.handler_stats),
            "recent_alerts": len(performance_monitor.get_performance_alerts()),
        },
    }


@monitoring_router.get("/router/decisions/recent")
async def get_recent_router_decisions(limit: int = 50):
    """
    Get recent router decisions.

    Args:
        limit: Maximum number of decisions to return (default: 50, max: 200)
    """
    try:
        if limit <= 0 or limit > 200:
            raise HTTPException(
                status_code=400, detail="Limit must be between 1 and 200"
            )

        recent_decisions = list(performance_monitor.router_decisions)[-limit:]

        return {
            "decisions": [
                {
                    "timestamp": decision.timestamp,
                    "sender": decision.sender,
                    "message_preview": decision.message_preview,
                    "function_name": decision.function_name,
                    "confidence": decision.confidence,
                    "reasoning": decision.reasoning,
                    "execution_time": decision.execution_time,
                    "success": decision.success,
                }
                for decision in recent_decisions
            ],
            "total_returned": len(recent_decisions),
            "total_available": len(performance_monitor.router_decisions),
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting recent router decisions: {e}")
        raise HTTPException(
            status_code=500, detail="Failed to get recent router decisions"
        )


@monitoring_router.get("/handlers/{handler_name}/stats")
async def get_handler_stats(handler_name: str):
    """Get detailed statistics for a specific handler."""
    try:
        if handler_name not in performance_monitor.handler_stats:
            raise HTTPException(
                status_code=404, detail=f"Handler '{handler_name}' not found"
            )

        stats = performance_monitor.handler_stats[handler_name]

        # Get recent executions for this handler
        recent_executions = [
            execution
            for execution in performance_monitor.handler_executions
            if execution.handler_name == handler_name
        ][
            -20:
        ]  # Last 20 executions

        return {
            "handler_name": handler_name,
            "statistics": stats,
            "recent_executions": [
                {
                    "timestamp": execution.timestamp,
                    "sender": execution.sender,
                    "execution_time": execution.execution_time,
                    "success": execution.success,
                    "error_message": execution.error_message,
                }
                for execution in recent_executions
            ],
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting handler stats for {handler_name}: {e}")
        raise HTTPException(status_code=500, detail="Failed to get handler statistics")


@monitoring_router.post("/metrics/reset")
async def reset_metrics():
    """Reset all performance metrics (admin endpoint)."""
    try:
        performance_monitor.reset_metrics()
        logger.info("Performance metrics reset via API")
        return {"message": "Performance metrics reset successfully"}
    except Exception as e:
        logger.error(f"Error resetting metrics: {e}")
        raise HTTPException(status_code=500, detail="Failed to reset metrics")


@monitoring_router.get("/config/langfuse")
async def get_langfuse_config():
    """Get LangFuse configuration status."""
    return validate_langfuse_config()


@monitoring_router.get("/traces/active")
async def get_active_traces():
    """Get information about currently active traces."""
    try:
        active_traces = {}
        for trace_id, trace_info in whatsapp_tracer.active_traces.items():
            active_traces[trace_id] = whatsapp_tracer.get_trace_summary(trace_id)

        return {"active_traces": active_traces, "total_active": len(active_traces)}
    except Exception as e:
        logger.error(f"Error getting active traces: {e}")
        raise HTTPException(status_code=500, detail="Failed to get active traces")


@monitoring_router.get("/traces/summary")
async def get_tracing_summary():
    """Get summary of tracing system status."""
    langfuse_status = validate_langfuse_config()

    return {
        "tracing_enabled": langfuse_status["enabled"] and langfuse_status["connected"],
        "langfuse_status": langfuse_status,
        "active_traces_count": len(whatsapp_tracer.active_traces),
        "dashboard_url": get_monitoring_dashboard_url(),
        "trace_components": [
            "webhook_received",
            "user_validation",
            "media_processing",
            "message_buffering",
            "session_state_check",
            "router_classification",
            "handler_execution",
            "openai_assistant_call",
            "whatsapp_response_sent",
        ],
    }
