from app.utils.whatsapp_handler import send_whatsapp_message
from app.utils import get_active_window
from app.utils.secure_http_client import get_secure_http_client
from datetime import datetime


async def create_request_from_whatsapp(sender, phone_number, team_request_payload):
    # ✅ Send request to the team request API
    for request in team_request_payload:
        request["position"] = request["position"].lower()
        request["max_value"] = request["transfer_fee"]
        request["max_net_salary"] = request["asking_salary"]
        if not request["foot"]:
            request["foot"] = "no_preference"
        request["transfer_period"] = [get_active_window(datetime.now())]

        # Include contact person in the payload if available
        if "contact_person" in request and request["contact_person"]:
            request["club_contact"] = request["contact_person"]
        else:
            request["club_contact"] = None
    # Make secure API call
    client = get_secure_http_client()
    api_response = client.post(
        "/team_requests/whatsapp/bulk",
        json_data=team_request_payload,
        params={"phone_number": phone_number},
        sender=sender,
    )

    # Handle response
    if api_response.success:
        tokens_left = api_response.data.get("tokens_left", "N/A")
        send_whatsapp_message(
            sender, f"✅ Team request successfully created! Tokens left: {tokens_left}"
        )
    else:
        # Handle specific business logic errors (not auth/rate limit - those are handled by client)
        if api_response.status_code == 402:
            tokens_left = api_response.data.get("tokens_left", 0)
            send_whatsapp_message(
                sender,
                f"⚠ Not enough tokens to create team request! Tokens left: {tokens_left}",
            )
        elif api_response.status_code not in [401, 403, 429]:
            # Don't send additional messages for auth/rate limit errors (handled by client)
            send_whatsapp_message(
                sender, f"⚠ Failed to create team request. Please try again."
            )
