# WhatsApp Bot Router Agent Restructure Plan

## Overview
Transform the current if-else routing logic into a sophisticated router agent architecture using GPT-4o with OpenAI function calling, configurable handlers, and comprehensive monitoring.

## Architecture Diagram

```mermaid
graph TD
    A[WhatsApp Message] --> B[whatsapp_llm.py]
    B --> C[Router Agent GPT-4o]
    C --> D{Message Classification}
    
    D -->|Player Request| E[route_to_player_handler]
    D -->|Team Request| F[route_to_team_handler]
    D -->|Activity Task| G[route_to_activity_handler]
    D -->|Statistics Request| H[route_to_statistics_handler]
    D -->|Team Report| I[route_to_report_handler]
    D -->|Unclassified| J[handle_unclassified_message]
    
    E --> K[handle_player_request.py]
    F --> L[handle_team_request.py]
    G --> M[handle_activity_request.py]
    H --> N[handle_statistics_request.py]
    I --> O[handle_report_request.py]
    J --> P[Error Response]
    
    K --> Q[OpenAI Assistant Player]
    L --> R[OpenAI Assistant Team]
    M --> S[OpenAI Assistant Activity]
    N --> T[OpenAI Assistant Statistics]
    O --> U[OpenAI Assistant Report]
    
    Q --> V[WhatsApp Response]
    R --> V
    S --> V
    T --> V
    U --> V
    P --> V
    
    style C fill:#e1f5fe
    style D fill:#f3e5f5
    style V fill:#e8f5e8
```

## Implementation Steps

### Phase 1: Setup and Configuration

#### Step 1: Install Dependencies
```bash
pip install langsmith pyyaml
```

#### Step 2: Create Router Configuration
Create `router_config.yaml` in project root:

```yaml
router:
  model: "gpt-4o"
  temperature: 0.1
  max_tokens: 150
  
handlers:
  player_handler:
    name: "Player Request Handler"
    description: "Handles player analysis requests with Transfermarkt URLs"
    function_name: "route_to_player_handler"
    enabled: true
    
  team_handler:
    name: "Team Request Handler" 
    description: "Handles team requests"
    function_name: "route_to_team_handler"
    enabled: true
    
  activity_handler:
    name: "Activity Task Handler"
    description: "Handles football activity task creation and management"
    function_name: "route_to_activity_handler"
    enabled: false  # Will be implemented later
    
  statistics_handler:
    name: "Statistics Request Handler"
    description: "Handles football statistics and data requests"
    function_name: "route_to_statistics_handler" 
    enabled: false  # Will be implemented later
    
  report_handler:
    name: "Team Report Handler"
    description: "Handles team report generation requests"
    function_name: "route_to_report_handler"
    enabled: false  # Will be implemented later

error_handling:
  default_message: "I couldn't understand your request. Please try rephrasing or contact support."
  log_unclassified: true
  
monitoring:
  langsmith_enabled: true
  log_performance: true
  track_handler_usage: true
```

#### Step 3: Update Environment Variables
Add to `.env`:
```
LANGCHAIN_TRACING_V2=true
LANGCHAIN_ENDPOINT=https://api.smith.langchain.com
LANGCHAIN_API_KEY=your_langsmith_api_key
LANGCHAIN_PROJECT=whatsapp-bot-router
```

### Phase 2: Core Router Implementation

#### Step 4: Create Router Agent Class
Create `app/router/router_agent.py`:

```python
import yaml
import logging
from typing import Dict, Any, Optional
from openai import OpenAI
from langsmith import traceable
from app.config import settings

class RouterAgent:
    def __init__(self):
        self.client = OpenAI(api_key=settings.OPENAI_API_KEY)
        self.config = self._load_config()
        self.functions = self._build_function_definitions()
        
    def _load_config(self) -> Dict[str, Any]:
        with open('router_config.yaml', 'r') as file:
            return yaml.safe_load(file)
    
    def _build_function_definitions(self) -> list:
        # Build OpenAI function definitions from config
        pass
    
    @traceable
    async def route_message(self, message: str, sender: str) -> Dict[str, Any]:
        # Main routing logic with OpenAI function calling
        pass
```

#### Step 5: Create Handler Registry
Create `app/router/handler_registry.py`:

```python
from typing import Dict, Callable, Any
from app.players.handle_player_request import process_player_request
from app.teams.handle_team_request import handle_team_request

class HandlerRegistry:
    def __init__(self):
        self.handlers: Dict[str, Callable] = {}
        self._register_handlers()
    
    def _register_handlers(self):
        # Register all available handlers
        pass
    
    async def execute_handler(self, handler_name: str, **kwargs) -> Any:
        # Execute the appropriate handler
        pass
```

### Phase 3: Refactor Main Application

#### Step 6: Update whatsapp_llm.py
Modify the main routing logic to use the router agent:

```python
from app.router.router_agent import RouterAgent
from app.router.handler_registry import HandlerRegistry

router_agent = RouterAgent()
handler_registry = HandlerRegistry()

async def process_final_message(sender, merged_message):
    # ... existing session handling logic ...
    
    # Use router agent instead of if-else logic
    routing_result = await router_agent.route_message(merged_message, sender)
    
    if routing_result['success']:
        await handler_registry.execute_handler(
            routing_result['handler'],
            sender=sender,
            message=merged_message,
            client=client,
            user_sessions=user_sessions
        )
    else:
        # Handle routing errors
        send_whatsapp_message(sender, routing_result['error_message'])
```

### Phase 4: Monitoring Setup

#### Step 7: Setup LangSmith Integration
Create `app/monitoring/langsmith_setup.py`:

```python
import os
from langsmith import Client
from langchain.callbacks import LangChainTracer

def setup_langsmith():
    os.environ["LANGCHAIN_TRACING_V2"] = "true"
    client = Client()
    tracer = LangChainTracer(project_name="whatsapp-bot-router")
    return client, tracer
```

#### Step 8: Add Performance Monitoring
Create `app/monitoring/performance_monitor.py`:

```python
import time
import logging
from typing import Dict, Any
from datetime import datetime

class PerformanceMonitor:
    def __init__(self):
        self.metrics: Dict[str, Any] = {}
    
    def track_handler_performance(self, handler_name: str, duration: float):
        # Track handler execution times
        pass
    
    def log_routing_decision(self, message: str, handler: str, confidence: float):
        # Log routing decisions for analysis
        pass
```

### Phase 5: Testing and Validation

#### Step 9: Create Test Suite
Create `tests/test_router_agent.py`:

```python
import pytest
from app.router.router_agent import RouterAgent

class TestRouterAgent:
    def test_player_request_routing(self):
        # Test player request classification
        pass
    
    def test_team_request_routing(self):
        # Test team request classification  
        pass
    
    def test_unclassified_message_handling(self):
        # Test error handling for unclear messages
        pass
```

#### Step 10: Integration Testing
Create comprehensive integration tests to ensure:
- Router correctly classifies different message types
- Handlers receive correct parameters
- Error handling works as expected
- Performance monitoring captures metrics

### Phase 6: Deployment and Monitoring

#### Step 11: Update Docker Configuration
Update `Dockerfile` and `requirements.txt` with new dependencies.

#### Step 12: Deploy with Monitoring
- Deploy to your existing Cloud Run setup
- Configure LangSmith dashboard
- Set up alerts for routing failures
- Monitor handler performance metrics

## File Structure After Implementation

```
app/
├── api/
│   └── whatsapp_llm.py (modified)
├── router/
│   ├── __init__.py
│   ├── router_agent.py
│   └── handler_registry.py
├── monitoring/
│   ├── __init__.py
│   ├── langsmith_setup.py
│   └── performance_monitor.py
├── players/
│   └── handle_player_request.py (minimal changes)
├── teams/
│   └── handle_team_request.py (minimal changes)
└── utils/
    └── is_player_request.py (can be deprecated)

router_config.yaml (new)
tests/
└── test_router_agent.py (new)
```

## Benefits of This Architecture

1. **Scalability**: Easy to add new handlers without modifying core logic
2. **Maintainability**: Clear separation of concerns
3. **Monitoring**: Comprehensive tracking of agent performance
4. **Flexibility**: Configuration-driven handler management
5. **Reliability**: Robust error handling for edge cases
6. **Intelligence**: GPT-4o provides sophisticated message classification

## Next Steps After Implementation

1. Implement remaining handlers (activity, statistics, reports)
2. Add learning capabilities based on usage patterns
3. Implement A/B testing for different routing strategies
4. Add user feedback mechanisms for routing corrections
5. Optimize performance based on monitoring data

## Estimated Implementation Time

- Phase 1-2: 2-3 days
- Phase 3: 1-2 days  
- Phase 4: 1 day
- Phase 5-6: 1-2 days

**Total: 5-8 days**
